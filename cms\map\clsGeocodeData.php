<?php

  class clsGeocodeData {
    /*		Properties		*/
    protected $m_oMySQL = null;
    protected static $s_sGeocodeNormal = '`rde_cms`.`crm_addresses`';

    /*		Methods			*/
    // Constructor
    // - Public
    // Fires up database
    // Input:	-
    // Output:	-
    public function __construct() {
      $this->m_oMySQL = clsDBSingleton::instantiate();
    }

    // Destructor
    // - Public
    // Helps out the PHP garbadge collector
    // Input:	-
    // Output:	-
    public function __destruct() {
      unset($this->m_oMySQL);
    }

    // getGeocode
    // - Public
    // Retrieves latitues and longitudes from database
    // Input:	locations (array) ('country' => $sCountry, 'zipcode' => $p_sZipcode, 'street' => $p_sStreet)
    // Output:	geocodes (array)
    public function getGeocode($p_aLocations) {

      $sQueryAdd = '';
      #echo 'Going to search '.count($p_aLocations).' locations.<br />';

      # 01. Generate query with locations to search for
      for ($i = 0; $i < count($p_aLocations); $i++) {

        if($p_aLocations[$i]['zipcode'] != '' && strlen($p_aLocations[$i]['country']) == 2) {
          if($p_aLocations[$i]['country'] == 'NL') {
            $sQueryAdd .= 'OR (`zipcode` = \'' . $p_aLocations[$i]['zipcode'] . '\' AND `country` = \'NL\') ';
          }
          else {
            $sQueryAdd .= 'OR (`zipcode` = \'' . $p_aLocations[$i]['zipcode'] . '\' AND street = ' . $this->m_oMySQL->quote($p_aLocations[$i]['street']) . ' AND country = \'' . $p_aLocations[$i]['country'] . '\') ';
          }
        }
      }

      $sQueryAdd = substr($sQueryAdd, 3);

      # 02. Get longitude & latitude
      $aLatLng = [];
      $sQuery1 = 'SELECT
							`zipcode`,
							`street`, 
							`country`,
							`longitude`,
							`latitude`
						FROM
							' . self::$s_sGeocodeNormal . '
						WHERE
							' . $sQueryAdd;

      $sQuery1 .= 'ORDER BY longitude ASC';

      # 02.01 set normal geocodes
      foreach ($this->m_oMySQL->query($sQuery1) as $aGeocode) {

        if($aGeocode['country'] == 'NL') {
          $aLatLng['NL'][$aGeocode['zipcode']] = ['lat' => $aGeocode['latitude'], 'lng' => $aGeocode['longitude']];
        }
        else {
          $aLatLng[$aGeocode['country']][$aGeocode['zipcode']][$aGeocode['street']] = ['lat' => $aGeocode['latitude'], 'lng' => $aGeocode['longitude']];
        }
      }

      return $aLatLng;

    }

  }

?>