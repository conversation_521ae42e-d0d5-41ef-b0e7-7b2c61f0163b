<?php

  require_once($_SERVER['DOCUMENT_ROOT'] . '/config/headercode_global.inc.php');
  $oAuth = new clsAuth();
  $oAuth->validate();

  include_once($_SERVER['DOCUMENT_ROOT'] . '/cms/framework/classes/clsMySQL.php');
  clsDBSingleton::instantiate();

  $quotationId = $_GET['id'];
  $oDataQuotationExtra = clsDBSingleton::dbcSelectOne('rde_b1mo.quotations_extra', 'quotationId', $quotationId, '*');
  $oDataAddress = clsDBSingleton::dbcSelectOne('rde_cms.crm_addresses', 'addressId', $oDataQuotationExtra->addressDeliveryId, '*');

  $jsonData = '{"addressData":[' . "\n";
  $jsonData .= '   {';
  $jsonData .= '"addressId":"' . $oDataAddress->addressId . '"';
  $jsonData .= ',' . "\n";
  $jsonData .= '"companyId":"' . $oDataAddress->companyId . '"';
  $jsonData .= ',' . "\n";
  $jsonData .= '"type":"' . $oDataAddress->type . '"';
  $jsonData .= ',' . "\n";
  $jsonData .= '"title":"' . $oDataAddress->title . '"';
  $jsonData .= ',' . "\n";
  $jsonData .= '"street":"' . $oDataAddress->street . '"';
  $jsonData .= ',' . "\n";
  $jsonData .= '"nr":"' . $oDataAddress->nr . '"';
  $jsonData .= ',' . "\n";
  $jsonData .= '"extension":"' . $oDataAddress->extension . '"';
  $jsonData .= ',' . "\n";
  $jsonData .= '"zipcode":"' . $oDataAddress->zipcode . '"';
  $jsonData .= ',' . "\n";
  $jsonData .= '"domestic":"' . $oDataAddress->domestic . '"';
  $jsonData .= ',' . "\n";
  $jsonData .= '"country":"' . $oDataAddress->country . '"';
  $jsonData .= ',' . "\n";
  $jsonData .= '"longitude":"' . $oDataAddress->longitude . '"';
  $jsonData .= ',' . "\n";
  $jsonData .= '"latitude":"' . $oDataAddress->latitude . '"';
  $jsonData .= ',' . "\n";
  $jsonData .= '"extraInfo":"' . $oDataAddress->extraInfo . '"';
  $jsonData .= ',' . "\n";
  $jsonData .= '"date":"' . $oDataAddress->date . '"';
  $jsonData .= ',' . "\n";
  $jsonData .= '"mapExtra":"' . $oDataAddress->mapExtra . '" }';
  $jsonData .= "\n" . ']}';

  echo $jsonData;

