/**
 *
 * @param quotationId
 */
function sendEmailByQuote(quotationId) {

    $.ajax({
        type: 'GET',
        dataType: "json",
        data: {group: quotationId},
        url: "/cms/crm/ajax/sendEmail/ajaxSendEmailToClients.php",
        success: function(data) {

            if (data.error !== '') {

                if (data.error === 'false') {

                    //-- update the quotationId
                    var quoteAndType = quotationId.split("_");
                    var quotationIdOnly = quoteAndType[0];
                    var type = quoteAndType[1];
                    var routeId = quoteAndType[2];
                    var truckName = quoteAndType[3];

                    if (type === 'dlv') {
                        $("#chkQuotationsUpdateRouteDisplay" + quotationId).html('<img class="popupEmailSent" src="/cms/images/icons/email-sent-blue.png" title="delivery email sent" alt="delivery email sent">');
                        $("#imageEmailSent_"+quotationIdOnly+"_" + routeId).prepend('<img id="emailDelivery_'+routeId+'_'+truckName+'" class="imageEmailSent" src="/cms/images/icons/email-sent-blue.png" title="delivery email sent" alt="delivery email sent">');
                        $("#chkQuotationsUpdateRouteDisplay" + quotationIdOnly + '_remdlv_' + routeId).html('<input type="checkbox" class="selectAllRemoveLink'+truckName+'" name="chkQuotations" value="'+quotationIdOnly+'_remdlv_'+routeId+'_'+truckName+'">');
                    }

                    if (type === 'pick') {
                        $("#chkQuotationsUpdateRouteDisplay" + quotationId).html('<img class="popupEmailSent" src="/cms/images/icons/email-sent-green.png" title="pickup email sent" alt="pickup email sent">');
                        $("#imageEmailSent_"+quotationIdOnly+"_" + routeId).prepend('<img id="emailPickup_'+routeId+'_'+truckName+'" class="imageEmailSent" src="/cms/images/icons/email-sent-green.png" title="pickup email sent" alt="pickup email sent">');
                        $("#chkQuotationsUpdateRouteDisplay" + quotationIdOnly + '_rempick_' + routeId).html('<input type="checkbox" class="selectAllRemoveLink'+truckName+'" name="chkQuotations" value="'+quotationIdOnly+'_rempick_'+routeId+'_'+truckName+'">');
                    }

                    if (type === 'remdlv') {
                        $("#chkQuotationsUpdateRouteDisplay" + quotationId).html('<img class="popupEmailSent" src="/cms/images/icons/email-sent-red.png" title="remove from route email sent" alt="remove from route email sent">');
                        var nameDLV = '#chkQuotationsUpdateRouteDisplay' +  quotationIdOnly + '_remdlv_' + routeId + '_' + truckName;
                        if ($(nameDLV).length > 0) {
                            $("#imageEmailSent_"+quotationIdOnly+"_" + routeId).prepend('<img id="emailRemove_'+routeId+'" class="imageEmailSent" src="/cms/images/icons/email-sent-red.png" title="remove from route email sent" alt="remove from route email sent">');
                        }
                        $("#chkQuotationsUpdateRouteDisplay" + quotationId).html('<img class="popupEmailSent" src="/cms/images/icons/email-sent-red.png" title="remove from route email sent" alt="remove from route email sent">');
                    }

                    if (type === 'rempick') {
                        var namePICK = '#chkQuotationsUpdateRouteDisplay' +  quotationIdOnly + '_rempick_' + routeId + '_' + truckName;
                        if ($(namePICK).length > 0) {
                            $("#imageEmailSent_"+quotationIdOnly+"_" + routeId).prepend('<img id="emailRemove_'+routeId+'" class="imageEmailSent" src="/cms/images/icons/email-sent-red.png" title="remove from route email sent" alt="remove from route email sent">');
                        }
                        $("#chkQuotationsUpdateRouteDisplay" + quotationId).html('<img class="popupEmailSent" src="/cms/images/icons/email-sent-red.png" title="remove from route email sent" alt="remove from route email sent">');
                    }

                } else {

                    $("#showErrorsSendEmail").html('Error message: ' + data.errorMessage);

                }

            }
        },
        error: function(jqXHR, exception) {
            console.log('error code LXzkp9J9');
            if (jqXHR.status === 0) {
                console.log('Not connect.n Verify Network.');
                alert('Not connect.n Verify Network.');
            } else if (jqXHR.status == 404) {
                console.log('Requested page not found. [404]');
                alert('Requested page not found. [404]');
            } else if (jqXHR.status == 500) {
                console.log('Internal Server Error [500].');
                alert('Internal Server Error [500].');
            } else if (exception === 'parsererror') {
                console.log('Requested JSON parse failed. possible that its not well formatted.');
                alert('Requested JSON parse failed. possible that its not well formatted.');
            } else if (exception === 'timeout') {
                console.log('Time out error.');
                alert('Time out error.');
            } else if (exception === 'abort') {
                console.log('Ajax request aborted.');
                alert('Ajax request aborted.');
            } else {
                console.log('Uncaught Error.n' + jqXHR.responseText);
                alert('Uncaught Error.n' + jqXHR.responseText);
            }
        }
    }); // $.ajax({

}
