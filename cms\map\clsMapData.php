<?php

  /**
   * Class clsMapData
   */
  class clsMapData {
    /*		Properties		*/

    # Framework
    protected $m_oMySQL = null;              # PDO

    # Table locations
    protected static $s_sRDEQuotes = '`rde_b1mo`.`quotations`';
    protected static $s_sRDEQuotesExtra = '`rde_b1mo`.`quotations_extra`';
    protected static $s_sRDEQuoteContainers = '`rde_b1mo`.`containers_quotations`';
    protected static $s_sRDEContainers = '`rde_b1mo`.`containers`';
    protected static $s_sRDEUsers = '`rde_b1mo`.`sandbox_users`';
    protected static $s_sRDEProductionOrder = '`rde_b1mo`.`production_order`';
    protected static $s_sRDEAddresses = '`rde_cms`.`crm_addresses`';

    protected $m_aStatus = [];
    protected $m_bShowContainers = false;
    protected $m_bShowSmallOrders = false;
    protected $m_bShowMissingOrders = true;
    protected $m_bShowGpsBuddy = true;
    protected $m_bShowPoi = true;
    protected $m_sCountry = null;
    protected $m_sZipcode = null;
    protected $m_sStreet = null;
    protected $m_sNr = null;
    protected $m_sZipcode2 = null;
    protected $m_bMarkerLocNoQuoteExtra = false;
    protected $m_iQuotationId = -1;
    protected $m_sStartDate = '';
    protected $m_sEndDate = '';

    protected $showBrands = [];


    /*		Methods			*/

    // Constructor
    // - Public
    // Enables MySQL connection
    // Input:	-
    // Output:	-
    public function __construct() {
      $this->m_oMySQL = clsDBSingleton::instantiate();
    }

    // Getter
    // - Public
    // Returns property value
    // Input:	variable name (string)
    // Output:	variable value (mixed)
    public function __get($p_sVarName = '') {
      switch ($p_sVarName) {
        case 'status':
          return (array)$this->m_aStatus;
          break;
        case 'showContainers':
          return (bool)$this->m_bShowContainers;
          break;
        case 'showSmallOrders':
          return (bool)$this->m_bShowSmallOrders;
          break;
        case 'showBrands':
          return (array)$this->showBrands;
          break;
        case 'showGpsBuddy':
          return (bool)$this->m_bShowGpsBuddy;
          break;
        case 'showMissingOrders':
          return (bool)$this->m_bShowMissingOrders;
          break;
        case 'showPoi':
          return (bool)$this->m_bShowPoi;
          break;
        case 'quotationId':
          return (int)$this->m_iQuotationId;
          break;
        case 'startDateMapData':
          return (string)$this->m_sStartDate;
          break;
        case 'endDateMapData':
          return (string)$this->m_sEndDate;
          break;
        case 'markerLocNoQuoteExtra':
          return (bool)$this->m_bMarkerLocNoQuoteExtra;
          break;
        default:
          return false;
      }
    }

    // Setter
    // - Public
    // Sets value to class property
    // Input:	variable name (string), variable value (mixed)
    // Output:	-
    public function __set($p_sVarName, $p_mVarValue) {
      switch ($p_sVarName) {
        case 'status':
          // 'prepare', 'produce', 'pending', 'deliver', 'loaded', 'delivered'
          if(in_array($p_mVarValue, ['20', '30', '35', '38', '40', '50', '53', '55'])) {
            $this->m_aStatus[] = $p_mVarValue;
          }
          break;
        case 'showContainers':
          if($p_mVarValue === true) {
            $this->m_bShowContainers = true;
          }
          else {
            $this->m_bShowContainers = false;
          }
          break;
        case 'showSmallOrders':
          if($p_mVarValue === true) {
            $this->m_bShowSmallOrders = true;
          }
          else {
            $this->m_bShowSmallOrders = false;
          }
          break;
        case 'showBrands':
          $this->showBrands = $p_mVarValue;
          break;
        case 'showGpsBuddy':
          if($p_mVarValue === true) {
            $this->m_bShowGpsBuddy = true;
          }
          else {
            $this->m_bShowGpsBuddy = false;
          }
          break;
        case 'showMissingOrders':
          if($p_mVarValue === true) {
            $this->m_bShowMissingOrders = true;
          }
          else {
            $this->m_bShowMissingOrders = false;
          }
          break;
        case 'showPoi':
          if($p_mVarValue === true) {
            $this->m_bShowPoi = true;
          }
          else {
            $this->m_bShowPoi = false;
          }
          break;

        case 'startDateMapData':
          $this->m_sStartDate = $p_mVarValue;
          break;
        case 'endDateMapData':
          $this->m_sEndDate = $p_mVarValue;
          break;
        case 'country':
          $this->m_sCountry = $p_mVarValue;
          break;
        case 'zipcode':
          $this->m_sZipcode = $p_mVarValue;
          break;
        case 'street':
          $this->m_sStreet = $p_mVarValue;
          break;
        case 'nr':
          $this->m_sNr = $p_mVarValue;
          break;
        case 'zipcode2':
          $this->m_sZipcode2 = $p_mVarValue;
          break;
        case 'quotationId':
          $this->m_iQuotationId = $p_mVarValue;
          break;
        case 'markerLocNoQuoteExtra':
          if($p_mVarValue === true) {
            $this->m_bMarkerLocNoQuoteExtra = true;
          }
          else {
            $this->m_bMarkerLocNoQuoteExtra = false;
          }
          break;
      }
    }


    /*
     * queryObjectResult
     * input: sql query
     * return: object or false
     */

    public function queryObjectSingleResult($sQuery) {
      $oResult = $this->m_oMySQL->query($sQuery);
      if($oResult === false) {
        return false;
      }

      return $oResult->fetchObject();

    }

    /**
     * @return bool|mixed
     */
    public function checkIfAddressDeliveryIdExist() {

      $sQuery = " SELECT addressDeliveryId
                    FROM rde_b1mo.quotations_extra
                    WHERE quotationId = '" . $this->m_iQuotationId . "'
                  ";

      return $this->queryObjectSingleResult($sQuery);

    }

    /**
     * @param $companyId
     * @return bool|mixed
     */
    public function getCompanyContainerDoNotReturn($companyId) {

      $sQuery = " SELECT containerDoNotReturn
                    FROM rde_cms.crm_companies
                    WHERE companyId = '" . $companyId . "'
                  ";

      return $this->queryObjectSingleResult($sQuery);

    }

    /**
     * @return bool|mixed
     */
    public function checkIfCoordinatesDoNotExist() {

      $sQuery = " SELECT qe.quotationId
                    FROM rde_b1mo.quotations_extra qe
                    LEFT JOIN rde_cms.crm_addresses a ON a.addressId = qe.addressDeliveryId 
                    WHERE 
                          (qe.quotationId = '" . $this->m_iQuotationId . "'
                            AND a.longitude = '0.000000000000000'
                            AND a.latitude = '0.000000000000000') 
                    OR
                          (qe.quotationId = '" . $this->m_iQuotationId . "'
                            AND a.longitude IS NULL
                            AND a.latitude iS NULL)
                     ";

      return $this->queryObjectSingleResult($sQuery);

    }

    /**
     * @return bool|mixed
     */
    public function checkIfCoordinatesDoNotExistDeliveryIsZero() {

      $sQuery = " SELECT qe.quotationId
                    FROM rde_b1mo.quotations_extra qe
                    WHERE qe.quotationId = '" . $this->m_iQuotationId . "'
                    AND qe.addressDeliveryId = '0' ";

      return $this->queryObjectSingleResult($sQuery);

    }


    /**
     * @param $companyId
     * @return bool|mixed
     */
    public function getCompanyNameByCompanyId($companyId) {

      $sQuery = " SELECT name 
                    FROM rde_cms.crm_companies 
                    WHERE companyId = '" . $companyId . "' 
                    ORDER BY companyId DESC ";

      return $this->queryObjectSingleResult($sQuery);

    }


    /**
     * @return bool|mixed
     */
    public function checkIfQuoteExtraExists() {

      $sQuery = "SELECT * FROM rde_b1mo.quotations_extra WHERE quotationId = '" . $this->m_iQuotationId . "' ";

      return $this->queryObjectSingleResult($sQuery);

    }

    private array $stoneTypes = [];

    /**
     * @param $quotationId
     * @return mixed
     */
    public function getStoneTypeId($quotationId) {

      $sQuery = "SELECT stoneId FROM rde_b1mo.quotations WHERE quotationId = '" . $quotationId . "' ";
      $oQuotationData = $this->queryObjectSingleResult($sQuery);

      if($oQuotationData->stoneId === '0') {
        return false;
      }

      if(isset($this->stoneTypes[$oQuotationData->stoneId])) {
        return $this->stoneTypes[$oQuotationData->stoneId];
      }


      $sqlStoneData = "SELECT brandId FROM rde_b1mo.stones WHERE stoneId = '" . $oQuotationData->stoneId . "' ";
      $oStoneData = $this->queryObjectSingleResult($sqlStoneData);
      if($oStoneData===false) return false;

      $sqlStoneBrandsData = "SELECT stoneTypeId FROM rde_b1mo.stone_brands WHERE brandId = '" . $oStoneData->brandId . "' ";
      $oStoneBrandsData = $this->queryObjectSingleResult($sqlStoneBrandsData);

      $this->stoneTypes[$oQuotationData->stoneId] = $oStoneBrandsData->stoneTypeId;

      return $oStoneBrandsData->stoneTypeId;

    }

    /**
     * @return bool|PDOStatement
     * @throws Exception
     */
    public function getAddressType() {

      $sQuery = "SELECT
                            type
                    FROM 
                            rde_cms.crm_addresses
                    WHERE
                            zipcode = '" . $this->m_sZipcode2 . "'
                    AND
                            nr = '" . $this->m_sNr . "'
                    AND 
                            type IN ('visit', 'map', 'delivery')
                    LIMIT 
                            1";

      $oResult1 = $this->m_oMySQL->query($sQuery);

      if($oResult1 === false) {
        throw new Exception('Failed to get marker locations');
      }
      $oResult1->execute();

      return $oResult1;

    }

    /**
     * @return bool
     * @throws Exception
     */
    public function getCompanyId() {

      $sQuery = "SELECT
                            companyId
                    FROM 
                            rde_b1mo.quotations
                    WHERE
                            quotationId = '" . $this->quotationId . "'
                    LIMIT 
                            1";

      $oResult1 = $this->m_oMySQL->query($sQuery);

      if($oResult1 === false) {
        throw new Exception('Failed to get companyId');
      }

      $oResult1->execute();
      $oResult1CompanyId = $oResult1->fetchAll();

      return $oResult1CompanyId[0]['companyId'];

    }

    /**
     * @return bool|PDOStatement
     * @throws Exception
     */
    public function getMarkerLocNoQuoteExtra() {

      $sQuery1 = null;
      $sQuery2 = null;

      if(count($this->status) < 1 && $this->showContainers === false) {
        # Nothing selected, which will go wrong
        // $this->status = 'deliver';
        $this->status = '50';
      }

      # Orders
      if(count($this->status) > 0) {

        $sQuery1 = "SELECT

								REPLACE(O.`zipcode`, ' ', '') AS `geo_zipcode`,
                                CONCAT(O.street, ' ', O.nr, ' ', O.ext) AS geo_street,
                                O.nr AS geo_nr,
								
								IF(O.`nextRouteDate` IS NOT NULL,
									CONCAT(O.`statusId`, IF(COUNT(B.`containerId`) > 0, 'Bd', 'd')),
									CONCAT(O.`statusId`, IF(COUNT(B.`containerId`) > 0, 'B', ''))
								) AS `icon`,
									
								1 AS `orderCount`,
								O.`quotationId` AS `orderId`
                                                                
							FROM
								" . self::$s_sRDEQuotes . " O
							INNER JOIN
								" . self::$s_sRDEUsers . " U
							ON
								O.`userId` = U.`userId`	
							LEFT JOIN
								" . self::$s_sRDEQuoteContainers . " BO
							USING (`quotationId`)
                            
							LEFT JOIN
								" . self::$s_sRDEContainers . " B
							USING (`containerId`)
                            
							WHERE
								O.`zipcode` != ''
							AND
								O.`statusId` IN ('" . implode("', '", $this->status) . "')
							AND
                                LOWER(TRIM(O.`zipcode`)) NOT IN ('montage', 'afhalen', 'korting', 'stjoris', 'intro', 'ventiklik')
							GROUP BY
								O.`quotationId`
							";
      }

      # Containers
      if($this->showContainers) {

        $sQuery2 = "SELECT
								REPLACE(O.`zipcode`, ' ', '') AS `geo_zipcode`,
                                                                
                                CONCAT(O.street, ' ', O.nr, ' ', O.ext) AS geo_street,

                                O.nr AS geo_nr,

								IF(BO.`nextroute` = 'true',
									CONCAT(B.`type`, 'd'),
									B.`type`
								) AS `icon`,
								
								1 AS `orderCount`,
								B.`containerId` AS `orderId`
                                
							FROM
								" . self::$s_sRDEQuotes . " O 
							INNER JOIN
								" . self::$s_sRDEQuoteContainers . " BO
							USING (`quotationId`)
							INNER JOIN
								" . self::$s_sRDEContainers . " B
							USING (`containerId`)

							WHERE
								O.`statusId` != '10'
							AND
								LOWER(TRIM(O.`zipcode`)) NOT IN ('montage', 'afhalen', 'korting')
							AND
								B.`inStock` =  'N'
							AND
								BO.`returnDate` IS NULL
							GROUP BY
								B.`containerId`
							";
      }

      if($sQuery1 != '' && $sQuery2 != '') {
        $sQueryC = "(( {$sQuery1} ) UNION ( {$sQuery2} )) AS `markers` ";

      }
      else {
        $sQueryC = "( " . $sQuery1 . $sQuery2 . " ) AS `markers` ";

      }

      $sQuery = "SELECT 
						`geo_zipcode`,
						`geo_street`,
                        geo_nr,
						`icon`,
						orderId
					FROM 
						{$sQueryC}
					LIMIT 
						0, 550";

      $oResult1 = $this->m_oMySQL->query($sQuery);

      if($oResult1 === false) {
        throw new Exception('Failed to get marker locations');
      }

      $oResult1->execute();

      return $oResult1;

    }


    /**
     * @return bool|PDOStatement
     * @throws Exception
     */
    public function getMarkerLocationsPOI() {

      $sQuery = "

                            SELECT 
                                * 
                            FROM 
                                " . self::$s_sRDEAddresses . "
                            WHERE 
                                `mapExtra` = 1

							";

      $oResult1 = $this->m_oMySQL->query($sQuery);

      if($oResult1 === false) {
        throw new Exception('Failed to get marker locations');
      }

      $oResult1->execute();

      return $oResult1;

    }

    /**
     * @return array
     * @throws Exception
     */
    public function getMarkerLocationsMapData() {

      $sQuery1 = null;
      $sQuery2 = null;

      if(count($this->status) < 1 && $this->showContainers === false) {
        # Nothing selected, which will go wrong
        // $this->status = 'deliver';
        $this->status = '50';
      }

      $newArrayProductionDate = [];

      # Orders
      if(is_array($this->status) && count($this->status) > 0) {

        $sQuery1 = "SELECT
			                    V.productionDate,
			                    O.dueDate,
			                    O.companyId,
			                    O.quotationId,
                          O.urgencyFlag,
                          O.userId,
								          REPLACE(O.`zipcode`, ' ', '') AS `geo_zipcode`,
                          CONCAT(O.street, ' ', O.nr, ' ', O.ext) AS geo_street,
                          O.nr AS geo_nr,
								IF (O.`nextRouteDate` IS NOT NULL OR O.urgencyFlag = 1,
									CONCAT(O.`statusId`, IF(B.containerId IS NOT NULL, 'Bd', 'd')),
									CONCAT(O.`statusId`, IF(B.containerId IS NOT NULL, 'B', ''))
								)AS `icon`,
								1 AS `orderCount`,
								O.`quotationId` AS `orderId`";

        if($this->markerLocNoQuoteExtra === false) {
          $sQuery1 .= " , QE.addressDeliveryId, A.longitude, A.latitude ";
        }

        $sQuery1 .= " FROM " . self::$s_sRDEQuotes . " O								
								INNER JOIN " . self::$s_sRDEUsers . " U ON O.`userId` = U.`userId`
								LEFT JOIN " . self::$s_sRDEQuoteContainers . " BO USING (`quotationId`) 
							  LEFT JOIN " . self::$s_sRDEProductionOrder . " V USING (`quotationId`)
							";

        if($this->markerLocNoQuoteExtra === false) {
          $sQuery1 .= "INNER JOIN " . self::$s_sRDEQuotesExtra . " QE USING (`quotationId`)
                       INNER JOIN " . self::$s_sRDEAddresses . " A ON A.`addressId` = QE.addressDeliveryId ";
        }

        if($this->showSmallOrders === true) {
          $sqlSmallOrdersSetting = "SELECT setting FROM rde_b1mo.settings WHERE name = 'mapSmallOrders' LIMIT 1 ";
          $rSmallOrdersSetting = $this->m_oMySQL->query($sqlSmallOrdersSetting);
          $rSmallOrdersSetting->execute();
          $oSmallOrdersSetting = $rSmallOrdersSetting->fetchAll();
          $sSmallOrdersSetting = $oSmallOrdersSetting[0]['setting'];
        }


        if($this->startDateMapData != '') {
          $sQuery1 .= "
							LEFT JOIN " . self::$s_sRDEContainers . " B USING (`containerId`)
							WHERE
							TRIM(O.`zipcode`) != ''
							AND O.`statusId` IN ('" . implode("', '", $this->status) . "')
							AND LOWER(TRIM(O.`zipcode`)) NOT IN ('montage', 'afhalen', 'korting', 'stjoris', 'intro', 'ventiklik')
							AND
							( 
							(O.dueDate >= '" . $this->startDateMapData . "' AND O.dueDate <= '" . $this->endDateMapData . "' )
							OR
							(V.productionDate >= '" . $this->startDateMapData . "' AND V.productionDate <= '" . $this->endDateMapData . "')
							)
							";

          if($this->showSmallOrders === true) {
            $sQuery1 .= " AND O.meters < " . $sSmallOrdersSetting . " ";
          }

          if($this->showSmallOrders === true) {
            $sQuery1 .= " AND O.meters < " . $sSmallOrdersSetting . " ";
          }

          if(!empty($this->showBrands)) {
            $sQuery1 .= " AND O.brandId IN (".implode(",", $this->showBrands).") ";
          }

          $oResult1 = $this->m_oMySQL->query($sQuery1);
          if($oResult1 === false) {
            throw new Exception('Failed to get marker locations');
          }

          $oResult1->execute();
          $oObjectQuery = $oResult1->fetchAll(PDO::FETCH_ASSOC);

          $quotationIdArray = [];

          $counter = 0;

          foreach ($oObjectQuery as $key => $value) {

            if(in_array($value['quotationId'], $quotationIdArray)) continue;

            if($this->showSmallOrders === true) {

              $stati = implode("', '", $this->status);
              $query       = <<<SQL
              SELECT SUM(O.meters) as meters FROM rde_b1mo.quotations O
              JOIN `rde_b1mo`.`quotations_extra` QE USING(`quotationId`)
              LEFT JOIN `rde_b1mo`.`production_order` V USING(`quotationId`)
              WHERE TRIM(O.`zipcode`) != '' 
              AND O.`statusId` IN('$stati') 
              AND (
                  (O.dueDate >= '$this->startDateMapData' AND O.dueDate <= '$this->endDateMapData ')
                  OR 
                  (V.productionDate >= '$this->startDateMapData' AND V.productionDate <= '$this->endDateMapData ')
              )
              AND LOWER(TRIM(O.`zipcode`)) NOT IN(
              'montage',
              'afhalen',
              'korting',
              'stjoris',
              'intro',
              'ventiklik'
              )
              SQL;
              $query .= " AND `addressDeliveryId` = '" . $value["addressDeliveryId"] . "' ";

              $metersQuery = $this->m_oMySQL->query($query);
              $metersQuery->execute();
              $row = $metersQuery->fetchObject();
              if(!$row || $row->meters>=$sSmallOrdersSetting) {
                //niet tonen, geen kleine order
                continue;
              }

            }

            //containers even ophalen, en toevoegen
            $quotationIdArray[] = $value['quotationId'];
            $sqlContainersMain = "SELECT GROUP_CONCAT(CONCAT(B.`type`, ' ',B.`containerNumber`) SEPARATOR ', ') AS `containers` 
                                  FROM " . self::$s_sRDEQuoteContainers . "
                                  LEFT JOIN " . self::$s_sRDEContainers . " B USING (`containerId`) 
                                  WHERE quotationId = '" . $value['quotationId'] . "' ";
            $resultContainersMain = $this->m_oMySQL->query($sqlContainersMain);
            $oContainersDataMain = $resultContainersMain->fetchObject();
            $value['containers'] = $oContainersDataMain->containers;

            $newArrayProductionDate[] = $value;
            $counter++;

          }

        }

      }

      # Containers
      if($this->showContainers) {

        $sQuery2 = "SELECT
                    B.`containerId`, 
                    REPLACE(O.`zipcode`, ' ', '') AS `geo_zipcode`,
                    CONCAT(O.street, ' ', O.nr, ' ', O.ext) AS geo_street,
                    BO.`nextroute`,
                    IF(BO.`nextroute` = 'true', CONCAT(B.`type`, 'd'), B.`type`) AS `icon`,
                    B.`containerId` AS `orderId`,
                    1 AS `orderCount`,
                    O.nr AS geo_nr,
                    V.productionDate,
                    O.dueDate,
                    O.companyId,
                    O.quotationId,
                    O.urgencyFlag
                    ";

        if($this->markerLocNoQuoteExtra === false) {

          $sQuery2 .= " , QE.addressDeliveryId,
                          A.longitude,
                          A.latitude ";

        }

        $sQuery2 .= "FROM " . self::$s_sRDEQuotes . " O 
							INNER JOIN " . self::$s_sRDEQuoteContainers . " BO USING (`quotationId`)
							INNER JOIN " . self::$s_sRDEContainers . " B USING (`containerId`)
							LEFT JOIN " . self::$s_sRDEProductionOrder . " V USING (`quotationId`)
							";

        if($this->markerLocNoQuoteExtra === false) {
          $sQuery2 .= "INNER JOIN " . self::$s_sRDEQuotesExtra . " QE USING (`quotationId`)
                       INNER JOIN " . self::$s_sRDEAddresses . " A ON A.`addressId` = QE.addressDeliveryId ";
        }

        $sQuery2 .= "WHERE ( ";
        $sQuery2 .= "O.`statusId` != '10' ";
        $sQuery2 .= "AND LOWER(TRIM(O.`zipcode`)) NOT IN ('montage', 'afhalen', 'korting') ";
        $sQuery2 .= "AND B.`inStock` =  'N' ";
        $sQuery2 .= "AND BO.`returnDate` IS NULL ";
        $sQuery2 .= ") ";

        if(!empty($this->showBrands)) {
          $sQuery2 .= " AND O.brandId IN (".implode(",", $this->showBrands).") ";
        }
//        pd($sQuery2);

//        $sQuery2 .= "AND containerId=114 ";
//        $sQuery2 .= "ORDER BY BO.`containerQuotationId` DESC";

        $oResult2 = $this->m_oMySQL->query($sQuery2);

        if($oResult2 === false) {
          //echo '<p>Kom ik hier? ' . __LINE__ . ' ' . __FILE__ . '</p>';
          throw new Exception('Failed to get marker locations');
        }


        $oResult2->execute();
        $oObjectQuery2 = $oResult2->fetchAll(PDO::FETCH_ASSOC);
        $counter = 0;
        //        $quotationIdArray = [];
        $containerIdArray = [];

        foreach ($oObjectQuery2 as $key2 => $value2) {

          if(in_array($value2['containerId'], $containerIdArray)) {
            //deze container zit al in de array, overslaan.
            //echter wel even controleren of deze order toevallig nextonroute is, zo ja dashing icon
            $key_of_main = array_search($value2['containerId'], $containerIdArray);
            if($value2["nextroute"] == "true" && substr($newArrayProductionDate[$key_of_main]["icon"],-1)!="d") {
              //hij is next route, en nog niet dashing
              $newArrayProductionDate[$key_of_main]["icon"] .= "d";
            }
            continue;
          }
          $containerIdArray[count($newArrayProductionDate)] = $value2['containerId'];

          $sqlContainersQuery2 = "SELECT 
                          GROUP_CONCAT(CONCAT(B.`type`, ' ',B.`containerNumber`) SEPARATOR ', ') AS `containers` 
                      FROM 
                          " . self::$s_sRDEQuoteContainers . "_rde
                      LEFT JOIN
                    " . self::$s_sRDEContainers . " B
                      USING (`containerId`) 
                      WHERE 
                          quotationId = '" . $value2['quotationId'] . "' ";

          $resultContainersQuery2 = $this->m_oMySQL->query($sqlContainersQuery2);
          $oContainersDataQuery2 = $resultContainersQuery2->fetchObject();

          $value2['containers'] = $oContainersDataQuery2->containers;

          $newArrayProductionDate[] = $value2;
          $counter++;

        }

      }


      //-- loop door de hele array heen en voeg postcodes samen
      //-- maar ik moet altijd bakd voor bak laten gaan.
      //--
      $newArrayProductionDateGroupGeoZipcode = [];
      $geoZipcodeArrayCheck = [];
      $geoZipcodeArrayId = [];
      $zipcodeArrayCounter = 0;
      foreach ($newArrayProductionDate as $keyGeoZip => $valueGeoZip) {

        if(!in_array($valueGeoZip['geo_zipcode'], $geoZipcodeArrayCheck)) {

          //-- toevoegen aan array omdat deze zipcode nog niet bestaat
          $geoZipcodeArrayCheck[] = $valueGeoZip['geo_zipcode'];
          $geoZipcodeArrayId[$valueGeoZip['geo_zipcode']] = $zipcodeArrayCounter;
          $newArrayProductionDateGroupGeoZipcode[] = $valueGeoZip;
          $zipcodeArrayCounter++;

        }
        else {

          foreach ($newArrayProductionDateGroupGeoZipcode as $napKey => $napValue) {

            //-- Het kan wel eens voorkomen dat een container gevuld is met twee quotationId's
            //-- maar dat deze ene quotationId opgehaald moet worden en de anderen niet.

            if($valueGeoZip['geo_zipcode'] === $napValue['geo_zipcode'] && $valueGeoZip['icon'] !== $napValue['icon']) {

              if($valueGeoZip['icon'] === 'bakd' && $napValue['icon'] === 'bak') {
                $newArrayProductionDateGroupGeoZipcode[$napKey]['icon'] = 'bakd';
              }

              if($valueGeoZip['icon'] === 'rekd' && $napValue['icon'] === 'rek') {
                $newArrayProductionDateGroupGeoZipcode[$napKey]['icon'] = 'rekd';
              }

            }

          } // foreach ($newArrayProductionDateGroupGeoZipcode AS $napKey => $napValue) {

        } // if (!in_array($valueGeoZip['geo_zipcode'], $geoZipcodeArrayCheck)) {

      } // foreach ($newArrayProductionDate AS $keyGeoZip => $valueGeoZip) {

      //--------------------------------------------------------------------------------------------------------------

      $geoZipcodeArrayCheck2 = [];

      //-- ik denk dat ik door alle records moet lopen.
      foreach ($newArrayProductionDate as $keyProductionDate => $valueProductionDate) {

        if(in_array($valueProductionDate['geo_zipcode'], $geoZipcodeArrayCheck2)) {

          //          if($valueProductionDate['geo_zipcode']=="6097ND") {
          //            pd($valueProductionDate);
          //          }

          //-- update new array
          $newArrayProductionDateGroupGeoZipcode[$geoZipcodeArrayId[$valueProductionDate['geo_zipcode']]]['orderCount']++;

          $containersSplitArray = explode(",", (string)$newArrayProductionDateGroupGeoZipcode[$geoZipcodeArrayId[$valueProductionDate['geo_zipcode']]]['containers']);
          $containersSplitArrayUnique = array_unique($containersSplitArray);
          $stringCleanedUpArray = '';
          $cleanedUpArrayCounter = 0;
          foreach ($containersSplitArrayUnique as $keyUniqueArray => $valueUniqueArray) {
            if($cleanedUpArrayCounter === 0) {
              $stringCleanedUpArray = $valueUniqueArray;
            }
            else {
              $stringCleanedUpArray = $stringCleanedUpArray . ',' . $valueUniqueArray;
            }
            $cleanedUpArrayCounter++;
          }
          $newArrayProductionDateGroupGeoZipcode[$geoZipcodeArrayId[$valueProductionDate['geo_zipcode']]]['containers'] = $stringCleanedUpArray;

        }
        else {

          //-- toevoegen aan array omdat deze zipcode nog niet bestaat
          $geoZipcodeArrayCheck2[] = $valueProductionDate['geo_zipcode'];

        }

      }

      //--------------------------------------------------------------------------------------------------------------

      foreach ($newArrayProductionDateGroupGeoZipcode as $keyNAPDGGeoZipcode => $valueNAPDGGeoZipcode) {

        $stoneTypeId = $this->getStoneTypeId($valueNAPDGGeoZipcode['quotationId']);

        if($stoneTypeId === false) {
          // echo '<p>$stoneTypeId: NO STONE TYPE ID</p>';
          $newArrayProductionDateGroupGeoZipcode[$keyNAPDGGeoZipcode]['stoneTypeId'] = '1';
        }
        else {
          $newArrayProductionDateGroupGeoZipcode[$keyNAPDGGeoZipcode]['stoneTypeId'] = $stoneTypeId;
          // echo '<p>$stoneTypeId: '.$stoneTypeId.' $valueNAPDGGeoZipcode[\'quotationId\']: '.$valueNAPDGGeoZipcode['quotationId'].'</p>';
        }

      }


      return $newArrayProductionDateGroupGeoZipcode;

    }

    // getOrdersMapData
    // - Public
    // Get order data on location
    // Input:	-
    // Output:	PDO statement or false
    public function getOrdersMapData() {

      $sQuery1 = "SELECT
							O.`quotationId` AS `orderId`,
							IF(O.`quotationPart` > 0, 
								CONCAT(O.`quotationNumber`, '.', O.`quotationVersion`, '-',CHAR(O.`quotationPart`)),
								CONCAT(O.`quotationNumber`, '.', O.`quotationVersion`)
							) AS `orderNumber`,
							
							U.`userId` AS `userId`,
							U.`companyId`,
							U.`companyName` AS `companyName`,
							O.`domestic` AS `deliverDomestic`,
                            O.`zipcode`,
							
							DATE_FORMAT(O.`productionDate`, '%d-%m-%y') AS `orderDate`,
							DATEDIFF(NOW(), O.`productionDate`) AS `daysOpen`,
							DATE_FORMAT(V.`productionDate`, '%d-%m-%y') AS `promisedDate`,
							O.`dueDateWeek` AS `promisedWeek`,
							
                            IF(O.`nextRouteDate` IS NOT NULL,
								CONCAT(O.`statusId`, IF(B.`containerId` IS NOT NULL, 'Bd', 'd')),
								CONCAT(O.`statusId`, IF(B.`containerId` IS NOT NULL, 'B', ''))
							) AS `icon`,
                            
							O.`statusId`,
							O.`nextRouteDate`,
							O.`meters`,
							O.`internNotes` AS `orderNotes`,
							BO.`nextroute` AS `nextRoute`,
							B.`containerId` AS `trueContainerId`,
							B.`type`,
							B.`containerNumber`,
							O.`stoneId`
							
						FROM
							" . self::$s_sRDEQuotes . ' O
						INNER JOIN
							' . self::$s_sRDEUsers . ' U
						ON
							O.`userId` = U.`userId`
						LEFT JOIN
							' . self::$s_sRDEQuoteContainers . ' BO
						USING (`quotationId`)
						LEFT JOIN
							' . self::$s_sRDEContainers . ' B
						USING (`containerId`)
						LEFT JOIN
							' . self::$s_sRDEProductionOrder . " V
						USING (`quotationId`)
						WHERE
							O.`statusId` NOT IN ('10', 'invoice', '70', '60', '61', '62', '63', '99', '80')
						AND
							REPLACE(O.`zipcode`, ' ', '') = " . $this->m_oMySQL->quote($this->m_sZipcode) . "
						";

      if($this->m_sCountry != 'NL' && $this->m_sStreet != '') {
        $sQuery1 .= "AND
							O.`street` LIKE '" . substr($this->m_oMySQL->quote($this->m_sStreet), 1, -1) . "%'
						";
      }

      $sQuery1 .= "
						ORDER BY
							O.`statusId` DESC,
							`orderNumber` ASC
						LIMIT 100";

      $oResult1 = $this->m_oMySQL->query($sQuery1);

      include_once($_SERVER['DOCUMENT_ROOT'] . '/cms/crm/classes/clsConvert.php');

      $oInfoConvert = new clsConvert();

      //-- deze code niet weg gooien. Te gebruiken bij testen
      // mail('<EMAIL>', 'test heblad', '<p>test heblad: <p>' .$sQuery1);

      //---------------------------------------------------------------------
      //-- leverdatum start
      //--
      //-- onderstaande code is om leverdatum toe te voegen aan de object

      if($oResult1 === false) {
        throw new Exception('Failed to retrieve orders on location');
      }

      $result2 = $this->m_oMySQL->query($oResult1->queryString);
      if($result2 === false) {
        $aData = null;
      }
      else {
        $aData = [];
        while ($oData = $result2->fetchObject()) {
          $aData[] = $oData;
        }

        foreach ($aData as $key => $value) {

          //-- haal alle bakken en rekken op vanaf hier.

          $sqlContainers = "SELECT 
                                          GROUP_CONCAT(CONCAT(B.`type`, ' ',B.`containerNumber`) SEPARATOR ', ') AS `containers` 
                                      FROM 
                                          " . self::$s_sRDEQuoteContainers . "_rde
                                      LEFT JOIN
                            " . self::$s_sRDEContainers . " B
                              USING (`containerId`) 
                                      WHERE 
                                          quotationId = '" . $value->orderId . "' ";

          $resultContainers = $this->m_oMySQL->query($sqlContainers);
          $oContainersData = $resultContainers->fetchObject();

          //-- get stoneTypeId
          // $value->stoneId

          $sqlStoneData = "SELECT brandId FROM rde_b1mo.stones WHERE stoneId = '" . $value->stoneId . "' ";
          $rStoneData = $this->m_oMySQL->query($sqlStoneData);
          $oStoneData = $rStoneData->fetchObject();

          //-- bij webshop items is er geen stone id

          if($oStoneData !== false) {
            $sqlStoneBrandsData = "SELECT stoneTypeId FROM rde_b1mo.stone_brands WHERE brandId = '" . $oStoneData->brandId . "' ";
            $rStoneBrandsData = $this->m_oMySQL->query($sqlStoneBrandsData);
            $oStoneBrandsData = $rStoneBrandsData->fetchObject();

            $aData[$key]->stoneTypeId = $oStoneBrandsData->stoneTypeId;
          }
          else {
            $aData[$key]->stoneTypeId = 'webshopitem';
          }


          //-- add containers to array
          $aData[$key]->containers = $oContainersData->containers;

          $sqlR = "SELECT MIN(routeId) as routeId FROM rde_route.gpsbuddy_rde WHERE quotationId = '" . $value->orderId . "' ";
          $resultGPSRDE = $this->m_oMySQL->query($sqlR);
          if($resultGPSRDE !== false) {
            $oGpsBuddyData = $resultGPSRDE->fetchObject();
            $sqlRoutes = "SELECT date FROM rde_route.gpsbuddy_routes WHERE routeId = '" . $oGpsBuddyData->routeId . "' ";
            $resultRoutes = $this->m_oMySQL->query($sqlRoutes);
            if($resultRoutes === false) {
              $oDateData = null;
            }
            else {
              $oDateData = $resultRoutes->fetchObject();
              if($oDateData !== false) {
                $aData[$key]->plannedDeliveryDate = $oInfoConvert->dbToDutchDate($oDateData->date);
              }
              else {
                $aData[$key]->plannedDeliveryDate = '';
              }
            }

          }
          else {
            $resultGPSRDE = null;
            //-- niks doen
            //-- deze route is nog niet ingeplanned
          }
        }

        return $aData;

      }

      //--
      //-- leverdatum einde
      //---------------------------------------------------------------------

    }

    // getContainers
    // - Public
    // Get all containers on location
    // Input:	-
    // Output:	PDOStatement or false
    public function getContainers() {
      if($this->showContainers) {
        $sQuery1 = "SELECT 
							O.`quotationId` AS `orderId`,
							IF(O.`quotationPart` > 0, 
								CONCAT(O.`quotationNumber`, '.', O.`quotationVersion`, '-', CHAR(O.`quotationPart`)),
								CONCAT(O.`quotationNumber`, '.', O.`quotationVersion`)
							) AS `orderNumber`,
							
							O.`userId` AS `companyId`,
              O.`companyId` AS `realCompanyId`,
							U.`companyname` AS `companyName`,
							O.`domestic` AS `deliverDomestic`,
							BO.`nextroute` AS `nextRoute`,
							B.`containerId` AS `trueContainerId`,
							
							IF(BO.`nextroute` = 'true', CONCAT(B.`type`, 'd'), B.`type`) AS `icon`,
							
							CONCAT(B.`type`, ' ', B.`containerNumber`) AS `containers`,
							
							O.`internNotes` AS `orderNotes`,
							
							B.`containerNumber` AS `containerId`
						FROM 
							" . self::$s_sRDEQuotes . ' O
						LEFT JOIN
							' . self::$s_sRDEQuoteContainers . ' BO
						USING (`quotationId`)
						LEFT JOIN
							' . self::$s_sRDEContainers . ' B
						USING (`containerId`)
						INNER JOIN
							' . self::$s_sRDEUsers . " U
						ON
							O.`userId`= U.`userId`
						WHERE
							O.`statusId` != '10'
						AND
							B.`inStock` = 'N'
						AND
							BO.`returnDate` IS NULL
						AND 
							LOWER(TRIM(O.`zipcode`)) NOT IN ('montage', 'afhalen', 'korting')
						AND
							REPLACE(O.`zipcode`, ' ', '') = " . $this->m_oMySQL->quote($this->m_sZipcode) . "
						";

        if($this->m_sCountry != 'NL' && $this->m_sStreet != '') {
          $sQuery1 .= "AND
								O.`street` LIKE '" . substr($this->m_oMySQL->quote($this->m_sStreet), 1, -1) . "%'
							";
        }

        $sQuery1 .= "
						ORDER BY
							BO.`containerQuotationId` DESC
						";

        $oResult1 = $this->m_oMySQL->query($sQuery1);

        if($oResult1 === false) {
          throw new Exception('Failed to retrieve orders on location');
        }

        return $oResult1;
      }
    }
  }

