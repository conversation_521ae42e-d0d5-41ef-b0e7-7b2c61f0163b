<script type="text/javascript">

  jQuery.event.special.touchstart = {
    setup: function (_, ns, handle) {
      if (ns.includes("noPreventDefault")) {
        this.addEventListener("touchstart", handle, {passive: false});
      } else {
        this.addEventListener("touchstart", handle, {passive: true});
      }
    }
  };

  function getContainerData(containerId, quotationId) {

    console.log('containerId: ' + containerId);
    console.log('quotationId: ' + quotationId);

    $.ajax({
      type: 'GET',
      dataType: "json",
      data: {id: containerId, quoteid: quotationId},
      url: "ajax/ajaxGetContainerData.php",
      success: function (data) {

        console.dir(data);

        var pickupNextRoute = '';
        if (data.addressData[0].nextroute == 'false') {
          pickupNextRoute = 'Niet ophalen';
        } else if (data.addressData[0].nextroute == 'true') {
          pickupNextRoute = 'Container ophalen';
        }
        $("#btnUpdateContainerId_" + containerId + "_" + quotationId).html(pickupNextRoute);

      },
      error: function (jqXHR, exception) {
        console.log('error code 2353677788944672899');
        if (jqXHR.status === 0) {
          console.log('Not connect.n Verify Network.');
          alert('Not connect.n Verify Network.');
        } else if (jqXHR.status == 404) {
          console.log('Requested page not found. [404]');
          alert('Requested page not found. [404]');
        } else if (jqXHR.status == 500) {
          console.log('Internal Server Error [500].');
          alert('Internal Server Error [500].');
        } else if (exception === 'parsererror') {
          console.log('Requested JSON parse failed.');
          alert('Requested JSON parse failed.');
        } else if (exception === 'timeout') {
          console.log('Time out error.');
          alert('Time out error.');
        } else if (exception === 'abort') {
          console.log('Ajax request aborted.');
          alert('Ajax request aborted.');
        } else {
          console.log('Uncaught Error.n' + jqXHR.responseText);
          alert('Uncaught Error.n' + jqXHR.responseText);
        }
      }
    }); // $.ajax({


  }

  function javascriptuncheckall() {
    $('#chk_20:checkbox:enabled').prop('checked', false);
    $('#chk_30:checkbox:enabled').prop('checked', false);
    $('#chk_35:checkbox:enabled').prop('checked', false);
    $('#chk_38:checkbox:enabled').prop('checked', false);
    $('#chk_40:checkbox:enabled').prop('checked', false);
    $('#chk_50:checkbox:enabled').prop('checked', false);
    $('#chk_53:checkbox:enabled').prop('checked', false);
    $('#chk_55:checkbox:enabled').prop('checked', false);
    $('#chk_containers:checkbox:enabled').prop('checked', false);
    $('#chk_Poi:checkbox:enabled').prop('checked', false);
    $('#chk_GpsBuddy:checkbox:enabled').prop('checked', false);
    $('#chkOntOrders:checkbox:enabled').prop('checked', false);
    $('#chk_small_orders:enabled').prop('checked', false);
    $('#chkBrandChinese:enabled').prop('checked', false);
    $('#chkBelgischArduin:enabled').prop('checked', false);

  }

  //-- deze waardes kan ik helaas niet hernoemen naar een getal omdat
  //-- een variable niet mag beginnen met een getal

  // produceB: { icon: 'icons/rd2B.png' },

  var customIcons = {
    prepare: {icon: 'icons/rd1.png'},
    prepareCement: {icon: 'icons/rd1c.png'},
    prepareNaturalStone: {icon: 'icons/rd1n.png'},
    prepared: {icon: 'icons/rd1d.gif'},
    prepareB: {icon: 'icons/rd1d.gif'},
    produce: {icon: 'icons/rd2.png'},
    produced: {icon: 'icons/rd2d.gif'},
    produceB: {icon: 'icons/rd2d.gif'},
    produceNaturalStone: {icon: 'icons/rd2n.png'},
    produceCement: {icon: 'icons/rd2c.png'},
    produceprint: {icon: 'icons/pp1.png'},
    produceprintNaturalStone: {icon: 'icons/pp1n.png'},
    produceprintCement: {icon: 'icons/pp1c.png'},
    produceprintB: {icon: 'icons/pp2.gif'},
    pending: {icon: 'icons/rd3.png'},
    pendingNaturalStone: {icon: 'icons/rd3n.png'},
    pendingCement: {icon: 'icons/rd3c.png'},
    pendingd: {icon: 'icons/rd3d.gif'},
    pendingB: {icon: 'icons/rd3d.gif'},
    deliver: {icon: 'icons/rd4.png'},
    deliverNaturalStone: {icon: 'icons/rd4n.png'},
    deliverCement: {icon: 'icons/rd4c.png'},
    delivered: {icon: 'icons/rd5.png'},
    deliveredNaturalStone: {icon: 'icons/rd5n.png'},
    deliveredCement: {icon: 'icons/rd5c.png'},
    deliveredBd: {icon: 'icons/rd5Bd.png'},
    loaded: {icon: 'icons/rd6.png'},
    loadedB: {icon: 'icons/rd6.png'},
    loadedBd: {icon: 'icons/rd6.png'},
    loadedNaturalStone: {icon: 'icons/rd6n.png'},
    loadedCement: {icon: 'icons/rd6c.png'},

    deliveredB: {icon: 'icons/rd5.png'},
    deliverd: {icon: 'icons/rd4d.gif'},
    deliverB: {icon: 'icons/rd4B.png'},
    deliverBd: {icon: 'icons/rd4Bd.png'},
    rek: {icon: 'icons/rek.png'},
    opr: {icon: 'icons/rek.png'},
    pal: {icon: 'icons/rek.png'},
    rekd: {icon: 'icons/rekd.gif'},
    bak: {icon: 'icons/bak.png'},
    bakd: {icon: 'icons/bakd.gif'},
    multi: {icon: 'icons/multi.png'},
    multid: {icon: 'icons/multid.gif'},
    truck: {icon: 'icons/gpsbuddy.gif'},
    poi: {icon: 'icons/poi.png'}
  };

  function in_array(needle, haystack) {
    var key = '';

    for (key in haystack) {
      if (haystack[key] == needle) {
        return true;
      }
    }

    return false;
  }

  function orderDetails(p_sDiv) {
    if (document.getElementById(p_sDiv).style.display == 'none') {
      document.getElementById(p_sDiv).style.display = 'block';
    } else {
      document.getElementById(p_sDiv).style.display = 'none';
    }
  }

  function getOrdersJava(p_sQueryString) {
    //-- deze code niet verwijderen het is namelijk nodig voor debugging.
    //-- bijv: http://rde-legacy.rde.localhost/cms/map37/orders.php?country=NL&zipcode=3861SC&street=Beurtschipper&orders=Y&containers=Y
    //-- deze code kun je dan uitvoeren lokaal en dan kijken waar het mis gaat.
    console.log('p_sQueryString: ' + p_sQueryString);
    var objAjax = new oAjax.sendRequest('orders.php?' + p_sQueryString, null, setOrders);
  }

  function setOrders() {

    // console.log('setOrders');
    // console.log('setOrders check 56');

    var xmlDoc = this.cRequest.responseXML.documentElement;
    // console.log('setOrders check 1');
    var xOrders = xmlDoc.getElementsByTagName('order');
    // console.log('setOrders check 2');
    var xContainers = xmlDoc.getElementsByTagName('container');
    // console.log('setOrders check 3');

    var aContainers = new Array();
    var aBakkenO = new Array();
    var aBakkenR = new Array();
    var aRekkenO = new Array();
    var aRekkenR = new Array();
    var sSummary = '';
    var sHTML = '';
    var sDisplay = 'none';

    // console.log('setOrders check2 123');

    /*
    if (document.getElementById('chk_containers').checked) {
        console.log('kom ik hier 168');
    } else {
        console.log('kom ik hier 170');
    }
    */

    // console.log('xOrders.length: ' + xOrders.length);
    // console.log('xContainers.length: ' + xContainers.length);

    /*
    if (document.getElementById('chk_containers').checked) {
        console.log('kom ik hier 168');
    } else {
        console.log('kom ik hier 170');
    }
    */

    if ((!document.getElementById('chk_containers').checked && xOrders.length <= 4) || (document.getElementById('chk_containers').checked && (xOrders.length + xContainers.length) <= 4)) {
      sDisplay = 'block';
    }

    /* # 1 - Show all containers */
    if (xOrders.length > 0) {

      for (i = 0; i < xOrders.length; i++) {

        if (xOrders[i].getElementsByTagName('containers')[0].hasChildNodes()) {

          sContainers = xOrders[i].getElementsByTagName('containers')[0].firstChild.nodeValue;

          aOrderContainers = sContainers.split(", ");

          for (var c = 0; c < aOrderContainers.length; c++) {
            if (in_array(aOrderContainers[c], aContainers) === false) {
              if (aOrderContainers[c].substr(0, 3) == 'bak') {
                aBakkenO.push(aOrderContainers[c]);
              }
              if (aOrderContainers[c].substr(0, 3) == 'rek') {
                aRekkenO.push(aOrderContainers[c]);
              }
              aContainers.push(aOrderContainers[c]);
            }
          }
        }
      }
    }

    if (xContainers.length > 0) {
      for (j = 0; j < xContainers.length; j++) {
        if (xContainers[j].getElementsByTagName('containers')[0].hasChildNodes()) {

          sContainer = xContainers[j].getElementsByTagName('containers')[0].firstChild.nodeValue;

          if (in_array(sContainer, aContainers) === false) {
            if (sContainer.substr(0, 3) == 'bak') {
              aBakkenR.push(sContainer);
            }
            if (sContainer.substr(0, 3) == 'rek') {
              aRekkenR.push(sContainer);
            }
            aContainers.push(sContainer);
          }
        }
      }
    }

    sSummary = '<p><b>Bestellingen</b> ' + aBakkenO.length + ' bakken - ' + aRekkenO.length + ' rekken <br />' +
      '<b>Retouren</b> ' + aBakkenR.length + ' bakken - ' + aRekkenR.length + ' rekken </p>';

    statusname = '';

    /*
    if (document.getElementById('chk_small_orders').checked) {
        console.log('chk_small_orders');
    }

    console.log('setOrders check3');

    console.log('setOrders v2');

    if (document.getElementById('chk_20').checked) {
        console.log('kom ik hier 233');
    } else {
        console.log('kom ik hier 235');
    }
    */

    /* # 2 - Show all orders*/
    if (document.getElementById('chk_20').checked || document.getElementById('chk_30').checked || document.getElementById('chk_35').checked || document.getElementById('chk_38').checked || document.getElementById('chk_40').checked || document.getElementById('chk_50').checked || document.getElementById('chk_55').checked) {

      // console.log('setOrders v3');

      for (i = 0; i < xOrders.length; i++) {

        //-- statusId tag name comes from xml file
        sStatus = xOrders[i].getElementsByTagName('statusId')[0].firstChild.nodeValue;
        stoneTypeId = xOrders[i].getElementsByTagName('stoneTypeId')[0].firstChild.nodeValue;

        // console.log('sStatus: ' + sStatus);
        // console.log('stoneTypeId: ' + stoneTypeId);

        //-- 20 = prepare
        if (xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '20') {

          if (stoneTypeId === '1') {
            statusname = 'prepare';
          }
          else if (stoneTypeId === '2') {
            statusname = 'prepareCement';
          }
          else if (stoneTypeId === '3') {
            statusname = 'prepareNaturalStone';
          }
          else {
            console.log("Onbekend steen type: "+stoneTypeId);
          }

          // statusname = 'prepare';
          //-- 30 = produce

        } else if (xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '20d') {
          statusname = 'prepareB';

        } else if (xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '30') {
          statusname = 'produce';

          if (stoneTypeId === '1') {
            statusname = 'produce';
          }
          if (stoneTypeId === '2') {
            statusname = 'produceCement';
          }
          if (stoneTypeId === '3') {
            statusname = 'produceNaturalStone';
          }

        } else if (xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '30d') {
          statusname = 'produceB';
        } else if (xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '35' || xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '38') {
          statusname = 'produceprint';

          if (stoneTypeId === '1') {
            statusname = 'produceprint';
          }
          if (stoneTypeId === '2') {
            statusname = 'produceprintCement';
          }
          if (stoneTypeId === '3') {
            statusname = 'produceprintNaturalStone';
          }

        } else if (xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '35d' || xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '38d') {
          statusname = 'produceprintB';
        } else if (xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '40') {
          statusname = 'pending';

          if (stoneTypeId === '1') {
            statusname = 'pending';
          }
          if (stoneTypeId === '2') {
            statusname = 'pendingCement';
          }
          if (stoneTypeId === '3') {
            statusname = 'pendingNaturalStone';
          }

        } else if (xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '40d') {
          statusname = 'pendingB';
        } else if (xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '50') {
          statusname = 'deliver';

          if (stoneTypeId === '1') {
            statusname = 'deliver';
          }
          if (stoneTypeId === '2') {
            statusname = 'deliverCement';
          }
          if (stoneTypeId === '3') {
            statusname = 'deliverNaturalStone';
          }

        } else if (xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '50Bd') {
          statusname = 'deliverBd';
        } else if (xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '50B') {
          statusname = 'deliverB';
        } else if (xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '55') {
          statusname = 'delivered';

          if (stoneTypeId === '1') {
            statusname = 'delivered';
          }
          if (stoneTypeId === '2') {
            statusname = 'deliveredCement';
          }
          if (stoneTypeId === '3') {
            statusname = 'deliveredNaturalStone';
          }

        } else if (xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '53') {
          statusname = 'loaded';

          if (stoneTypeId === '1') {
            statusname = 'loaded';
          }
          if (stoneTypeId === '2') {
            statusname = 'loadedCement';
          }
          if (stoneTypeId === '3') {
            statusname = 'loadedNaturalStone';
          }

        } else if (xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '53B') {
          statusname = 'loadedB';
        } else if (xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '53Bd') {
          statusname = 'loadedBd';
        } else if (xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '55B') {
          statusname = 'deliveredB';
        } else if (xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '55Bd') {
          statusname = 'deliveredBd';
        } else if (xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '60') {
          statusname = 'deliver';
        } else if (xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '61') {
          statusname = 'deliver';
        } else if (xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '62') {
          statusname = 'deliver';
        } else if (xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue == '63') {
          statusname = 'deliver';
        } else {
          statusname = xOrders[i].getElementsByTagName('icon')[0].firstChild.nodeValue;
        }

        if (i == 0) {
          sHTML += '<h1>' + xOrders[i].getElementsByTagName('deliverDomestic')[0].firstChild.nodeValue + '</h1>'
        }

        var plannedDeliveryDate = '';
        var plannedDeliveryDateBigP = '';
        if (xOrders[i].getElementsByTagName('plannedDeliveryDate')[0].firstChild.nodeValue !== '') {
          plannedDeliveryDate = '<li><b>INGEPLANNED: ' + xOrders[i].getElementsByTagName('plannedDeliveryDate')[0].firstChild.nodeValue + '</b></li>';
          var plannedDeliveryDateBigP = ' <span style="color:red;"><b>P</b></span>';
        }


        if (xOrders[i].getElementsByTagName('promisedDate')[0].innerHTML == '') {
          // console.log('promisedDate == leeg : ');

          sHTML += '<div class="order"> ' +
            '	<p class="title"><a href="javascript:void(0);" onclick="orderDetails(\'orderDetails' + i + '\');">	' +
            '	<img src="' + customIcons[statusname].icon + '" title="' + customIcons[statusname].icon + '" alt="' + statusname + '" border=\"0\" />	' +
            xOrders[i].getElementsByTagName('orderNumber')[0].firstChild.nodeValue +
            '	' + xOrders[i].getElementsByTagName('companyName')[0].firstChild.nodeValue + '</a>' + plannedDeliveryDateBigP + '</p>' +

            '	<div style=\"display: ' + sDisplay + ';\" id=\"orderDetails' + i + '\">	' +
            '		<ul class="minderenter">	' +

            plannedDeliveryDate +
            '           <li> <a href="#" title="(' + xOrders[i].getElementsByTagName('daysOpen')[0].firstChild.nodeValue + ') dagen geleden" >Besteldatum: ' + xOrders[i].getElementsByTagName('orderDate')[0].firstChild.nodeValue + '</a></li>	' +

            '			<li>Leverdatum: Nog niet ingeplanned</li>	' +

            '		</ul>	';


        } else {

          sHTML += '<div class="order"> ' +
            '	<p class="title"><a href="javascript:void(0);" onclick="orderDetails(\'orderDetails' + i + '\');">	' +
            '	<img src="' + customIcons[statusname].icon + '" title="' + customIcons[statusname].icon + '" alt="' + statusname + '" border=\"0\" />	' +
            xOrders[i].getElementsByTagName('orderNumber')[0].firstChild.nodeValue +
            '	' + xOrders[i].getElementsByTagName('companyName')[0].firstChild.nodeValue + '</a>' + plannedDeliveryDateBigP + '</p>' +

            '	<div style=\"display: ' + sDisplay + ';\" id=\"orderDetails' + i + '\">	' +
            '		<ul class="minderenter">	' +

            plannedDeliveryDate +
            '           <li> <a href="#" title="(' + xOrders[i].getElementsByTagName('daysOpen')[0].firstChild.nodeValue + ') dagen geleden" >Besteldatum: ' + xOrders[i].getElementsByTagName('orderDate')[0].firstChild.nodeValue + '</a></li>	' +
            '			<li>Leverdatum: ' + xOrders[i].getElementsByTagName('promisedDate')[0].firstChild.nodeValue + ' (week ' + xOrders[i].getElementsByTagName('promisedWeek')[0].firstChild.nodeValue + ')</li>	' +
            '		</ul>	';

        }

        /**
         * hasChildNodes controleert of deze waarde gevuld is
         */

        if (xOrders[i].getElementsByTagName('orderNotes')[0].hasChildNodes()) {
          sHTML += '	<ul class="minderenter"><li class="note">' + xOrders[i].getElementsByTagName('orderNotes')[0].firstChild.nodeValue + '</li></ul>';
        }

        sHTML += '		<ul class="minderenter">	' +
          '			<li>Meters: ' + xOrders[i].getElementsByTagName('meters')[0].firstChild.nodeValue + '<br /></li>	';

        if (xOrders[i].getElementsByTagName('containers')[0].hasChildNodes()) {
          sHTML += '			<li>Bakken: ' + xOrders[i].getElementsByTagName('containers')[0].firstChild.nodeValue + '</li>	';
        }

        sHTML += '		</ul>	' +
          '		<ul class="minderenter">	' +
          '			<li><a href="/cms/crm/crm.php?tabadmin=general&quotationId=' + xOrders[i].getElementsByTagName('orderId')[0].firstChild.nodeValue + '" target="_blank">Bekijk ' + xOrders[i].getElementsByTagName('orderNumber')[0].firstChild.nodeValue + '</a> </li>	';

        if (sStatus == '30' || sStatus == '35' || sStatus == '38' || sStatus == '40' || sStatus == '50' || sStatus == '53' || sStatus == '55') {

          //-- daniel
          if (xOrders[i].getElementsByTagName('companyId')[0].hasChildNodes()) {
            //sHTML += '			<li><a href=/nlnew/offerte/cms/print_vrachtbon.php?userid=' + xOrders[i].getElementsByTagName('userId')[0].firstChild.nodeValue + '&mutualid=' + xOrders[i].getElementsByTagName('companyId')[0].firstChild.nodeValue + '" target="_blank">Print vrachtbon</a></li>\n';
          } else {
            sHTML += '			<li>Deze bestelling heeft geen companyId gekoppeld</li>\n';
          }

        }

        sHTML += '			<li><a href="/cms/crm/crm.php?tabroute=route&quotationid=' + xOrders[i].getElementsByTagName('orderId')[0].firstChild.nodeValue + '" target="_blank">Plan voor route</a></li>	';

        sHTML += '		</ul>	' +
          '	</div> ' +
          '</div>';

      }
    }

    /*	# 3 - Show containers */
    if (document.getElementById('chk_containers').checked) {
      for (c = 0; c < xContainers.length; c++) {

        sHTML += '<div class="container">' +
          '	<a class="title" href="javascript:void(0);" onclick="orderDetails(\'containerDetails' + c + '\');"> ' +
          '<img src="' + customIcons[xContainers[c].getElementsByTagName('icon')[0].firstChild.nodeValue].icon + '" title="' + statusname + '" alt="' + statusname + '" border=\"0\" /> ' +
          xContainers[c].getElementsByTagName('containerId')[0].firstChild.nodeValue + ' ' +
          xContainers[c].getElementsByTagName('companyName')[0].firstChild.nodeValue + '</a>' +
          '	<div style=\"display: ' + sDisplay + ';\" class="containerdetails" id=\"containerDetails' + c + '\">' +
          '		<ul><li>Plaatsnaam: ' + xContainers[c].getElementsByTagName('deliverDomestic')[0].firstChild.nodeValue + '</li></ul> ';

        if (xContainers[c].getElementsByTagName('orderNotes')[0].hasChildNodes() && xContainers[c].getElementsByTagName('orderNotes')[0].firstChild.nodeValue!="") {
          sHTML += '<ul><li class="note">' + xContainers[c].getElementsByTagName('orderNotes')[0].firstChild.nodeValue + '</li></ul>';
        }

        if (xContainers[c].getElementsByTagName('companyId')[0].hasChildNodes()) {
          insertHTML = '';
        } else {
          insertHTML = '<li>Er is geen vrachtbon mogelijk omdat er geen companyId bestaat bij deze offerte</li>';
        }

        var btnUpdateContainerText = 'bak/rek switch';
        if (xContainers[c].getElementsByTagName('nextRoute')[0].firstChild.nodeValue == 'false') {
          btnUpdateContainerText = 'Niet ophalen';
        } else if (xContainers[c].getElementsByTagName('nextRoute')[0].firstChild.nodeValue == 'true') {
          btnUpdateContainerText = 'Container ophalen';
        }

        sHTML += '		<ul>' +
          '			<li><a href="/cms/crm/crm.php?tabadmin=general&quotationId=' + xContainers[c].getElementsByTagName('orderId')[0].firstChild.nodeValue + '" target="_blank">Bekijk ' + xContainers[c].getElementsByTagName('orderNumber')[0].firstChild.nodeValue + '</a></li>	' +
          insertHTML +
          '			<li><a href="/cms/crm/crm.php?tabroute=routecontainer&containerid=' + xContainers[c].getElementsByTagName('containerId')[0].firstChild.nodeValue + '" target="_blank">Plan voor route</a></li>' +
          '           <li>' +
          '               <span id="spanTrueContainerId" style="display:none;">' + xContainers[c].getElementsByTagName('trueContainerId')[0].firstChild.nodeValue + '</span>' +
          '               <span id="spanQuotationId" style="display:none;">' + xContainers[c].getElementsByTagName('orderId')[0].firstChild.nodeValue + '</span>' +
          '               <button type="button" id="btnUpdateContainerId_' + xContainers[c].getElementsByTagName('trueContainerId')[0].firstChild.nodeValue + '_' + xContainers[c].getElementsByTagName('orderId')[0].firstChild.nodeValue + '" onclick="getContainerData(' + xContainers[c].getElementsByTagName('trueContainerId')[0].firstChild.nodeValue + ', ' + xContainers[c].getElementsByTagName('orderId')[0].firstChild.nodeValue + ')" >' + btnUpdateContainerText + '</button>' +
          '           </li>' +
          '		</ul>' +
          '	</div>' +
          '</div>';

        // '               <button type="button" class="btnUpdateContainerClass" id="btnUpdateContainerId" >'+btnUpdateContainerText+'</button>' +
      }
    }

    document.getElementById('containerList').innerHTML = sSummary;
    document.getElementById('locationOrders').innerHTML = sHTML;
  }

  var map;

  function initialize() {

    var directionsService = new google.maps.DirectionsService;
    var directionsDisplay = new google.maps.DirectionsRenderer;

    //-- de instellingen van map_canvas
    var myOptions = {
      zoom: 8,
      center: new google.maps.LatLng(52.2, 5.5),
      mapTypeId: google.maps.MapTypeId.ROADMAP,
      panControl: true,
      zoomControl: true,
      mapTypeControl: true,
      scaleControl: true,
      streetViewControl: false,
      overviewMapControl: false
    };

    //-- hier wordt map_canvas gedefineerd
    map = new google.maps.Map(document.getElementById("map_canvas"), myOptions);

    //-- dit is de marker die toont waar de huidige selectie zit ingevoerd in de tekst veld
    //-- new york coordinaten
    //-- 40.730610
    //-- -73.935242

    //-- midden nederland cooordinaten
    //-- lat: 52.2,
    //-- lng: 5.5
    var marker = new google.maps.Marker({
      position: {
        lat: 40.730610,
        lng: -73.935242
      },
      map: map,
      draggable: false,
      title: 'Voer plaats in om in te zoemen op locatie'
    });

    var markers = [];

    directionsDisplay.setMap(map);


    function waypointSubmitTruckId(truckid) {
      $('#waypointSubmitTruckId'+truckid).click(function() {
        showDirections(directionsService, directionsDisplay, truckid);
        $('#directions-panel'+truckid).append('');
      })
    }

    <?php foreach ($trucks as $keyRoute => $valueRoute): ?>
      waypointSubmitTruckId(<?php echo $valueRoute->truckId ?>);
    <?php endforeach; ?>

    document.getElementById('resetCalculateAndDisplayRoute1').addEventListener('click', function() {
      removeDirectionResults(directionsService, directionsDisplay);
    },{passive: true});

    //-- dit is tijdelijk moet nog goed gemaakt worden en ook in de agenda getoond worden.
    var firstDateChosen = $("#fldRoutePlanningDate").val();
    var fddateJs = firstDateChosen.split("-");
    var fddutchDate = fddateJs[2] + "-" + fddateJs[1] + "-" + fddateJs[0];

    updateRouteOverview(fddutchDate, function () {
      console.log('Finished updateRouteOverview callback. 675');
    });

    function addMarker(label, latitude, longitude, wayptsGeoInfo) {

      var markerIcon = {
        url: '/cms/images/icons/map-marker-icon-64.png',
        scaledSize: new google.maps.Size(40, 40),
        origin: new google.maps.Point(0, 0),
        anchor: new google.maps.Point(20, 40),
        labelOrigin: new google.maps.Point(20, 15),
      };

      var marker = new google.maps.Marker({
        map: map,
        draggable: false,
        animation: google.maps.Animation.DROP,
        position: {lat: latitude, lng: longitude},
        icon: markerIcon,
        label: {
          text: label,
          color: "#FFFFFF",
          fontSize: "16px",
          fontWeight: "normal"
        }
      });

      marker.addListener('click', function () {
        getOrdersJava('country=' + wayptsGeoInfo[1] + '&zipcode=' + wayptsGeoInfo[0] + '&street=' + wayptsGeoInfo[2] + '&orders=Y&containers=Y');
      });

      markers.push(marker);

    }


    function showDirectionsWeek(directionsService, directionsDisplay, weekid) {

      updateUnplannedOverview(weekid, function () {
        //-- pass show directions uitvoeren als de callback klaar is
        showDirectionsMainWeek(directionsService, directionsDisplay, weekid);
        //-- add extra here
        showDirectionsMainWeekExtra(directionsService, directionsDisplay, weekid);
      });

    }

    function showDirections(directionsService, directionsDisplay, transportId) {

      var firstDateChosen = $("#fldRoutePlanningDate").val();
      var fddateJs = firstDateChosen.split("-");
      var fddutchDate = fddateJs[2] + "-" + fddateJs[1] + "-" + fddateJs[0];
      updateRouteOverview(fddutchDate, function () {
        //-- pass show directions uitvoeren als de callback klaar is
        console.log('Kom ik hier code: 3478h348fg34fg34ufyge36e');
        showDirectionsMain(directionsService, directionsDisplay, transportId);
      });

    }


    function showDirectionsMainWeek(directionsService, directionsDisplay, weekid) {

      removeMarkers();

      var combinedResults = [];
      combinedResults[0] = 0;
      combinedResults[1] = '';

      var total_size = $(".wayptsclassGeoInfo0").length;
      var wayptsAll = [];

      //-- Fabieksweg 5, Bladel - begin punt en eind punt
      var startValue = '51.3581276,5.20558';
      var endValue = '51.3581276,5.20558';
      wayptsAll.push({location: startValue, stopover: true});

      var counter = 0;
      // wayptsclass
      $(".wayptsclass0").each(function () {
        wayptsAll.push({location: this.value, stopover: true});
        counter++;
      });
      wayptsAll.push({location: endValue, stopover: true});

      var wayptsGeoInfoAll = [];

      //-- startLocation
      wayptsGeoInfoAll.push({geoInfo: '0000000'});

      $(".wayptsclassGeoInfo0").each(function () {
        wayptsGeoInfoAll.push({geoInfo: this.value});
        counter++;
      });

      //-- endLocation
      wayptsGeoInfoAll.push({geoInfo: '0000000'});

      var maxAmountofWaypoints = 25;
      var waypointLoops = parseInt(total_size) / maxAmountofWaypoints;
      waypointLoops = Math.floor(waypointLoops);
      var letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
      letters.push('AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ');
      var mainCounter = 0;
      var endValueCount = parseFloat(total_size) - 1;

      //-- bewaar nog even voor debugging.
      // console.log('maxAmountofWaypoints: ' + maxAmountofWaypoints);
      // console.log('endValueCount: ' + endValueCount);
      // console.log('waypointLoops: ' + waypointLoops);
      // console.log('total_size: ' + total_size);
      // console.log('maxAmountofWaypoints: ' + maxAmountofWaypoints);

      for (var i = 0; i <= waypointLoops; i++) {

        //-- 25 inclusief de start en end point
        //-- deze loop maar 25 keer uitvoeren
        //-- let op dat de start en end point eraf gaan
        //-- en dan resetten

        var waypts = [];
        for (var x = 0; x < maxAmountofWaypoints; x++) {
          //-- stoppen met uitvoeren als maximum is bereikt.
          if (mainCounter <= (total_size + 1)) {
            var wayptsLocation = wayptsAll[mainCounter].location;

            wayptsLocation = wayptsLocation.split(',');

            if (x == 0) {
              var startValue = parseFloat(wayptsLocation[0]) + ',' + parseFloat(wayptsLocation[1]);
            } else if (mainCounter == (endValueCount + 2) || x == (maxAmountofWaypoints - 2)) {
              var endValue = parseFloat(wayptsLocation[0]) + ',' + parseFloat(wayptsLocation[1]);
            } else {
              waypts.push({
                location: parseFloat(wayptsLocation[0]) + ',' + parseFloat(wayptsLocation[1]),
                stopover: true
              });
            }

            var wayptsGeoInfo = wayptsGeoInfoAll[mainCounter].geoInfo;
            wayptsGeoInfo = wayptsGeoInfo.split(',');

            addMarker(letters[mainCounter], parseFloat(wayptsLocation[0]), parseFloat(wayptsLocation[1]), wayptsGeoInfo);
            mainCounter++;

          }
        }
      }
    }

    function showDirectionsMainWeekExtra(directionsService, directionsDisplay, weekid) {

      var combinedResults = [];
      combinedResults[0] = 0;
      combinedResults[1] = '';

      var total_size = $(".wayptsclassGeoInfo0Extra").length;
      var wayptsAll = [];

      //-- Fabieksweg 5, Bladel - begin punt en eind punt
      var startValue = '51.3581276,5.20558';
      var endValue = '51.3581276,5.20558';
      wayptsAll.push({location: startValue, stopover: true});

      var counter = 0;
      $(".wayptsclass0extra").each(function () {
        wayptsAll.push({location: this.value, stopover: true});
        counter++;
      });
      wayptsAll.push({location: endValue, stopover: true});

      var wayptsGeoInfoAll = [];

      //-- startLocation
      wayptsGeoInfoAll.push({geoInfo: '0000000'});

      $(".wayptsclassGeoInfo0Extra").each(function () {
        wayptsGeoInfoAll.push({geoInfo: this.value});
        counter++;
      });

      //-- endLocation
      wayptsGeoInfoAll.push({geoInfo: '0000000'});

      var maxAmountofWaypoints = 25;
      var waypointLoops = parseInt(total_size) / maxAmountofWaypoints;
      waypointLoops = Math.floor(waypointLoops);
      var letters = ['XA', 'XB', 'XC', 'XD', 'XE', 'XF', 'XG', 'XH', 'XI', 'XJ', 'XK', 'XL', 'XM', 'XN', 'XO', 'XP', 'XQ', 'XR', 'XS', 'XT', 'XU', 'XV', 'XW', 'XX', 'XY', 'XZ'];
      letters.push('XAA', 'XAB', 'XAC', 'XAD', 'XAE', 'XAF', 'XAG', 'XAH', 'XAI', 'XAJ', 'XAK', 'XAL', 'XAM', 'XAN', 'XAO', 'XAP', 'XAQ', 'XAR', 'XAS', 'XAT', 'XAU', 'XAV', 'XAW', 'XAX', 'XAY', 'XAZ');
      var mainCounter = 0;
      var endValueCount = parseFloat(total_size) - 1;

      //-- bewaar nog even voor debugging.
      // console.log('maxAmountofWaypoints: ' + maxAmountofWaypoints);
      // console.log('endValueCount: ' + endValueCount);
      // console.log('waypointLoops: ' + waypointLoops);
      // console.log('total_size: ' + total_size);
      // console.log('maxAmountofWaypoints: ' + maxAmountofWaypoints);

      for (var i = 0; i <= waypointLoops; i++) {

        //-- 25 inclusief de start en end point
        //-- deze loop maar 25 keer uitvoeren
        //-- let op dat de start en end point eraf gaan
        //-- en dan resetten

        var waypts = [];
        for (var x = 0; x < maxAmountofWaypoints; x++) {
          //-- stoppen met uitvoeren als maximum is bereikt.
          if (mainCounter <= (total_size + 1)) {
            var wayptsLocation = wayptsAll[mainCounter].location;

            wayptsLocation = wayptsLocation.split(',');

            if (x == 0) {
              var startValue = parseFloat(wayptsLocation[0]) + ',' + parseFloat(wayptsLocation[1]);
            } else if (mainCounter == (endValueCount + 2) || x == (maxAmountofWaypoints - 2)) {
              var endValue = parseFloat(wayptsLocation[0]) + ',' + parseFloat(wayptsLocation[1]);
            } else {
              waypts.push({
                location: parseFloat(wayptsLocation[0]) + ',' + parseFloat(wayptsLocation[1]),
                stopover: true
              });
            }

            var wayptsGeoInfo = wayptsGeoInfoAll[mainCounter].geoInfo;
            wayptsGeoInfo = wayptsGeoInfo.split(',');

            addMarker(letters[mainCounter], parseFloat(wayptsLocation[0]), parseFloat(wayptsLocation[1]), wayptsGeoInfo);
            mainCounter++;

          }
        }
      }
    }


    function showDirectionsMain(directionsService, directionsDisplay, transportId) {

      removeMarkers();

      var combinedResults = [];
      combinedResults[0] = 0;
      combinedResults[1] = '';

      var total_size = $(".wayptsclass" + transportId).length;
      var wayptsAll = [];

      //-- Fabieksweg 5, Bladel - begin punt en eind punt
      var startValue = '51.3581276,5.20558';
      var endValue = '51.3581276,5.20558';
      wayptsAll.push({location: startValue, stopover: true});
      var counter = 0;
      $(".wayptsclass" + transportId).each(function () {
        wayptsAll.push({location: this.value, stopover: true});
        counter++;
      });
      wayptsAll.push({location: endValue, stopover: true});

      var wayptsGeoInfoAll = [];

      //-- startLocation
      wayptsGeoInfoAll.push({geoInfo: '0000000'});

      $(".wayptsclassGeoInfo" + transportId).each(function () {
        wayptsGeoInfoAll.push({geoInfo: this.value});
        counter++;
      });

      //-- endLocation
      wayptsGeoInfoAll.push({geoInfo: '0000000'});

      var maxAmountofWaypoints = 25;
      var waypointLoops = parseInt(total_size) / maxAmountofWaypoints;
      waypointLoops = Math.floor(waypointLoops);
      var letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
      letters.push('AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ');
      var mainCounter = 0;
      var endValueCount = parseFloat(total_size) - 1;

      //-- bewaar nog even voor debugging.
      // console.log('maxAmountofWaypoints: ' + maxAmountofWaypoints);
      // console.log('endValueCount: ' + endValueCount);

      for (var i = 0; i <= waypointLoops; i++) {

        //-- 25 inclusief de start en end point
        //-- deze loop maar 25 keer uitvoeren
        //-- let op dat de start en end point eraf gaan
        //-- en dan resetten

        var waypts = [];
        for (var x = 0; x < maxAmountofWaypoints; x++) {
          //-- stoppen met uitvoeren als maximum is bereikt.
          if (mainCounter <= (total_size + 1)) {
            var wayptsLocation = wayptsAll[mainCounter].location;
            wayptsLocation = wayptsLocation.split(',');

            if (x == 0) {
              var startValue = parseFloat(wayptsLocation[0]) + ',' + parseFloat(wayptsLocation[1]);
            } else if (mainCounter == (endValueCount + 2) || x == (maxAmountofWaypoints - 2)) {
              var endValue = parseFloat(wayptsLocation[0]) + ',' + parseFloat(wayptsLocation[1]);
            } else {
              waypts.push({
                location: parseFloat(wayptsLocation[0]) + ',' + parseFloat(wayptsLocation[1]),
                stopover: true
              });
            }

            var wayptsGeoInfo = wayptsGeoInfoAll[mainCounter].geoInfo;
            wayptsGeoInfo = wayptsGeoInfo.split(',');

            addMarker(letters[mainCounter], parseFloat(wayptsLocation[0]), parseFloat(wayptsLocation[1]), wayptsGeoInfo);
            mainCounter++;

          }
        }

        if (waypts.length > maxAmountofWaypoints) {
          console.log('to many waypoints');
        } else {
          if (i === 0) {
            combinedResults = createDirectionsResultsReturned(directionsService, directionsDisplay, startValue, endValue, waypts, combinedResults, waypointLoops, transportId)
          } else {
            // firstRoute === false will add to the route
            combinedResults = createDirectionsResultsReturned(directionsService, directionsDisplay, startValue, endValue, waypts, combinedResults, waypointLoops, transportId)
          }
        }
      }
    }

    function removeMarkers() {

      //-- reset the zoom
      var bounds = new google.maps.LatLngBounds();
      var boundLocation = new google.maps.LatLng(parseFloat(52.2), parseFloat(5.5));
      bounds.extend(boundLocation);
      map.fitBounds(bounds);
      map.setZoom(8);

      //-- remove the markers
      for (var i = 0; i < markers.length; i++) {
        markers[i].setMap(null);
      }

      markers = [];

    }

    function removeDirectionResults(directionsService, directionsDisplay) {

      //-- remove direction from map
      directionsDisplay.setDirections({routes: []});
      //-- remove markers from map
      removeMarkers();

    }

    function precisionRound(number, precision) {
      var factor = Math.pow(10, precision);
      return Math.round(number * factor) / factor;
    }

    function createDirectionsResultsReturned(directionsService, directionsDisplay, startValue, endValue, waypts, combinedResults, waypointLoops, transportId) {

      var polylineOptionsChoice = {
        strokeColor: '#0000FF',
        strokeOpacity: 0.5,
        strokeWeight: 5
      };

      //-- misschien later willen we de verschillende delen een andere kleur geven.
      // directionsDisplay.setOptions({polylineOptions: polylineOptionsChoice});
      directionsDisplay.setOptions({suppressMarkers: true});

      var request = {
        origin: startValue,
        destination: endValue,
        waypoints: waypts,
        travelMode: window.google.maps.TravelMode.DRIVING
      };
      directionsService.route(request, function (result, status) {
        if (status == window.google.maps.DirectionsStatus.OK) {
          if (combinedResults[0] == 0) { // first bunch of results in. new up the combinedResults object
            combinedResults[1] = result;
            combinedResults[0]++;
          } else {
            // only building up legs, overview_path, and bounds in my consolidated object. This is not a complete
            // directionResults object, but enough to draw a path on the map, which is all I need
            combinedResults[1].routes[0].legs = combinedResults[1].routes[0].legs.concat(result.routes[0].legs);
            combinedResults[1].routes[0].overview_path = combinedResults[1].routes[0].overview_path.concat(result.routes[0].overview_path);
            combinedResults[1].routes[0].bounds = combinedResults[1].routes[0].bounds.extend(result.routes[0].bounds.getNorthEast());
            combinedResults[1].routes[0].bounds = combinedResults[1].routes[0].bounds.extend(result.routes[0].bounds.getSouthWest());
            combinedResults[0]++;
          }

          if (combinedResults[0] == (waypointLoops + 1)) {

            // we have received all the results. put to map
            directionsDisplay.setDirections(combinedResults[1]);

            // var route = response.routes[0];
            var route = combinedResults[1].routes[0];
            $('div#directions-panel' + transportId).css("display", "inline-block");
            var summaryPanel = document.getElementById('directions-panel' + transportId);
            summaryPanel.innerHTML = '';
            // For each route, display summary information.
            var totalKm = 0;
            for (var i = 0; i < route.legs.length; i++) {
              var routeSegment = i + 1;
              // summaryPanel.innerHTML += '<b>Route Segment: ' + routeSegment + '</b><br>';
              // summaryPanel.innerHTML += route.legs[i].start_address + ' <b>to</b> ';
              // summaryPanel.innerHTML += route.legs[i].end_address + '<br><br>';
              // summaryPanel.innerHTML += route.legs[i].distance.text + '<br><br>';
              // var newDistanceValue = route.legs[i].distance.value / 1000;
              // summaryPanel.innerHTML += route.legs[i].distance.value + '<br><br>';
              totalKm += route.legs[i].distance.value;
              // summaryPanel.innerHTML += newDistanceValue + '<br><br>';
              // summaryPanel.innerHTML += precisionRound(newDistanceValue, 1) + '<br><br>';
            }

            totalKm = totalKm / 1000;
            totalKm = precisionRound(totalKm, 1);
            summaryPanel.innerHTML += 'Totaal: ' + totalKm + ' km';

          }

        }
        return combinedResults;
      });
      return combinedResults;

    }

    // Create the search box and link it to the UI element.
    var input = document.getElementById('mapsearch');
    var searchBox = new google.maps.places.SearchBox(input);
    map.controls[google.maps.ControlPosition.TOP_LEFT].push(input);

    // Bias the SearchBox results towards current map's viewport.
    map.addListener('bounds_changed', function () {
      searchBox.setBounds(map.getBounds());
    });

    google.maps.event.addListener(searchBox, 'places_changed', function () {
      var places = searchBox.getPlaces();
      var bounds = new google.maps.LatLngBounds();
      var i, place;
      for (i = 0; place = places[i]; i++) {
        bounds.extend(place.geometry.location);
        marker.setPosition(place.geometry.location);
      }
      map.fitBounds(bounds);
      map.setZoom(14);
    });

    function createMarker(l_marker) {
      if(customIcons[l_marker.icon] == undefined) {
        console.log("Onbekend icoon: ");
        console.log(l_marker);
        return;
      }

      var t_marker = new google.maps.Marker({
        position: new google.maps.LatLng(l_marker.lat, l_marker.lng),
        map: map,
        draggable: false,
        optimized: false,
        icon:  customIcons[l_marker.icon].icon
      });

      google.maps.event.addListener(t_marker, 'click', function() {
        getOrdersJava('country='+l_marker.country+'&zipcode='+l_marker.zipcode+'&street='+l_marker.steet+'&orders=Y&containers=Y');
      });
    }

    let marker_list = <?php echo json_encode($markers) ?>;
    for(let k in marker_list) {
      createMarker(marker_list[k]);
    }

    <?php


    if($aLastData[$transportIvecoId]->latitude != '') {

      echo "//--------------------------------------------\n";
      echo "//-- TRUCK IVECO start\n";
      echo "\n";

      echo "  var truckInfoIveco = new google.maps.InfoWindow({	\n";
      echo "      content:	'<h2>" . $currentLicensePlateIveco . "</h2><ul><li>Tijd: {$aLastData[$transportIvecoId]->received}</li></ul>' \n";
      echo "  });	\n\n";

      echo "  var truckMarkerIveco = new google.maps.Marker({  \n";
      echo "      position: new google.maps.LatLng({$aLastData[$transportIvecoId]->latitude}, {$aLastData[$transportIvecoId]->longitude}),	\n";
      echo "      map: map,	\n";
      echo "      icon: customIcons['truck'].icon	\n";
      echo "  });	\n\n";

      echo "  google.maps.event.addListener(truckMarkerIveco, 'click', function() {	\n";
      echo "      truckInfoIveco.open(map, truckMarkerIveco);	\n";
      echo "  });	\n\n";

      echo "//-- TRUCK IVECO end\n";
      echo "//--------------------------------------------\n";
      echo "\n";

    }


    if($aLastData[$transportVolvoId]->latitude != '') {

      echo "//--------------------------------------------\n";
      echo "//-- TRUCK VOLVO start\n";
      echo "\n";

      echo "  var truckInfo = new google.maps.InfoWindow({	\n";
      echo "      content:	'<h2>" . $currentLicensePlateVolvo . "</h2><ul><li>Tijd: {$aLastData[$transportVolvoId]->received}</li></ul>' \n";
      echo "  });	\n\n";

      echo "  var truckMarker = new google.maps.Marker({  \n";
      echo "	  position: new google.maps.LatLng({$aLastData[$transportVolvoId]->latitude}, {$aLastData[$transportVolvoId]->longitude}),	\n";
      echo "	  map: map,	\n";
      echo "	  icon: customIcons['truck'].icon	\n";
      echo "  });	\n\n";

      echo "  google.maps.event.addListener(truckMarker, 'click', function() {	\n";
      echo "	  truckInfo.open(map, truckMarker);	\n";
      echo "  });	\n\n";

      echo "//-- TRUCK VOLVO end\n";
      echo "//--------------------------------------------\n";
      echo "\n";

    }

    //-- Nuttige plaatsen = POI = Point of interest
    if($oMap->showPoi === true) {
      $oMap->printPoi();
    }

    ?>

  }	/*	Close initialize function */

  <?php

  if ($aLastData[$transportVolvoId]->latitude == '') {
  ?>
  function seeVolvo() {
    alert('geen locatie gegevens aanwezig Volvo. <?php echo $aLastData[$transportVolvoId]->latitude; ?>');
  }
  <?php
  } else {
  ?>
  function seeVolvo() {
    map.setCenter(new google.maps.LatLng(<?php echo $aLastData[$transportVolvoId]->latitude . ', ' . $aLastData[$transportVolvoId]->longitude; ?>));
    map.setZoom(10);
  }
  <?php
  }

  if ($aLastData[$transportIvecoId]->latitude == '') {
  ?>
  function seeIveco() {
    alert('geen locatie gegevens aanwezig Iveco.');
  }
  <?php
  } else {
  ?>
  function seeIveco() {
    map.setCenter(new google.maps.LatLng(<?php echo $aLastData[$transportIvecoId]->latitude . ', ' . $aLastData[$transportIvecoId]->longitude; ?>));
    map.setZoom(10);
  }
  <?php
  }

  ?>

</script>