<?php

  // http://rde-legacy.rde.localhost/cms/map/ajax/ajaxUpdateDateRoute.php?transportId=2&date=2018-06-01&olddate=2018-04-23

  require_once($_SERVER['DOCUMENT_ROOT'] . '/config/headercode_global.inc.php');
  $oAuth = new clsAuth();
  $oAuth->validate();

  include_once($_SERVER['DOCUMENT_ROOT'] . '/cms/framework/classes/clsMySQL.php');
  clsDBSingleton::instantiate();

  $transportId = $_GET['transportId'];
  $newdate = $_GET['date'];
  $olddate = $_GET['olddate'];

  //-- check if there are no planned routes on thuis date and transportId

  $sQuery = "SELECT
                        routeId, date, truckId
                    FROM
                        rde_route.gpsbuddy_routes
                    WHERE
                        truckId = '" . $transportId . "'
                    AND
                        date = '" . $newdate . "'
                    ";

  $oGpsBuddyRoutes = clsDBSingleton::processMultiObjectQuery($sQuery);

  $sError = '';

  if(count($oGpsBuddyRoutes) === 0) {

    //-- geen elementen in array

    //-- dan mogen we de datum wijzigen
    //-- controleer of de oude datum bestaat

    $sQueryOldDate = "SELECT
                                    routeId, date, truckId
                                FROM
                                    rde_route.gpsbuddy_routes
                                WHERE
                                    truckId = '" . $transportId . "'
                                AND
                                    date = '" . $olddate . "'
                                ";

    $oGpsBuddyRoutesOldDate = clsDBSingleton::processMultiObjectQuery($sQueryOldDate);

    if(count($oGpsBuddyRoutesOldDate) > 0) {
      foreach ($oGpsBuddyRoutesOldDate as $key => $value) {

        //-- tijdelijke uitschakkelen!!!!!!!!!!!!!!!!!!!!!!!
        $sqlUpdateDateRoute = "UPDATE rde_route.gpsbuddy_routes SET date = '" . $newdate . "' WHERE routeId = '" . $value->routeId . "' ";
        $result = clsDBSingleton::processUpdateOrInsertQuery($sqlUpdateDateRoute);

        //-- update email tabel aswel.
        //-- get route id
        //-- get the correct quotationId

        //------------------------------------------------------------------------------------------------------
        // verwijder alle quotations die zijn gekoppeld aan route id in tabel

        $sqlDelete = "DELETE FROM rde_b1mo.email_to_clients WHERE routeId = '" . $value->routeId . "';";
        $sqlError = 'error: 7BBJ3CTF delete from email_to_clients';
        clsDBSingleton::queryDelete($sqlDelete, $sqlError);

        //------------------------------------------------------------------------------------------------------

      }
    }
    else {
      $sError .= '{' . "\n";
      $sError .= '      "error": "Er bestaan geen routes op datum ' . $olddate . ' met transportId = ' . $transportId . '"';
      $sError .= '}' . "\n";
    }

  }
  else {

    $sError .= '{' . "\n";
    $sError .= '      "error": "Datum bestaat al op datum ' . $olddate . ' met transportId = ' . $transportId . '. Verwijder deze route eerst"';
    $sError .= '}' . "\n";

  }

  if($sError === '') {
    $sError = '{' . "\n";
    $sError .= '      "error": ""';
    $sError .= '}' . "\n";
    echo $sError;
  }
  else {
    echo $sError;
  }


