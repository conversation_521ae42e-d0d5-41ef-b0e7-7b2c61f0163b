<?php

  // http://rde-legacy.rde.localhost/cms/map/ajax/ajaxUpdateRank.php?dataRankIds=aid_20310-aid_20311-aid_20306

  require_once($_SERVER['DOCUMENT_ROOT'] . '/config/headercode_global.inc.php');
  $oAuth = new clsAuth();
  $oAuth->validate();

  include_once($_SERVER['DOCUMENT_ROOT'] . '/cms/framework/classes/clsMySQL.php');
  clsDBSingleton::instantiate();

  $dataRankIds = $_GET['dataRankIds'];
  $aDataRankIds = explode("-", $dataRankIds);

  $rankCounter = 0;
  foreach ($aDataRankIds as $key => $value) {
    $routeId = str_replace("aid_", "", $value);
    $routeId = str_replace("aidc_", "", $routeId);
    $routeId = str_replace("aidpoi_", "", $routeId);
    if(is_numeric($routeId)) {
      $sqlUpdateRank = "UPDATE rde_route.gpsbuddy_routes SET rank = '" . $rankCounter . "' WHERE routeId = '" . $routeId . "' ";
      $result = clsDBSingleton::processUpdateOrInsertQuery($sqlUpdateRank);
    }
    else {
      echo 'Error DJCpHpp2: only numbers allowed';
    }
    $rankCounter = $rankCounter + 5;
  }

  echo '{}';

