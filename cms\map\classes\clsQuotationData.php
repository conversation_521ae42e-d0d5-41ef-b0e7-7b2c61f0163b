<?php


  class clsQuotationData {

    /**
     * @param $oMySQL
     * @param $quotationId
     * @return mixed
     */
    public function getQuotationData($oMySQL, $quotationId) {

      $sqlProductionOrder =

        "SELECT 
                    dueDate, 
                    companyId, 
                    urgencyFlag, 
                    REPLACE(zipcode, ' ', '') AS geo_zipcode,
                    CONCAT(COALESCE(street,''),' ',COALESCE(nr,''),' ',COALESCE(ext,'')) AS geo_street,
                    nr AS geo_nr,
                    quotationId AS orderId,
                    nextRouteDate,
                    urgencyFlag,
                    statusId
                    
            FROM 
                    rde_b1mo.quotations 
            WHERE 
                    quotationId = '" . $quotationId . "' LIMIT 1 ";

      // echo '<p>\$sqlProductionOrder: '.$sqlProductionOrder.'</p>';

      $oResultProductionOrder = $oMySQL->query($sqlProductionOrder);
      $oData = $oResultProductionOrder->fetchObject();

      return $oData;

    }

  }