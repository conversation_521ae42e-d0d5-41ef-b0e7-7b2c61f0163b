<?php

  include_once('clsGeocodeData.php');

  class clsGeocode {

    /*		Properties		*/
    protected $m_oGeocodeData = null;
    protected $m_aLocations = [];

    /*		Methods			*/
    public function __constructor() {
    }

    public function __destruct() {
    }


    // getCountryByZipcode
    // - Public
    // Tracks the country based on the zipcode format
    // Input:	zipcode (string)
    // Output:	ISO country code (string) or false (bool)
    public function getCountryByZipcode($p_sZipcode = '') {
      if(preg_match('/^[1-9]{1}[0-9]{3}[\s]*[A-Z]{2}$/', $p_sZipcode)) {
        # Dutch zipcode
        return 'NL';
      }
      elseif(preg_match('/^B?[1-9]{1}[0-9]{3}$/', $p_sZipcode)) {
        # Belgian zipcode
        return 'BE';
      }
      elseif(preg_match('/^[0-9]{5}$/', $p_sZipcode)) {
        # German zipcode
        return 'DE';
      }
      else {
        return false;
      }
    }

    // addLocation
    // - Public
    // Adds locations which will be added to the query
    // Input:	reference id (int), zipcode (string), street (string)
    // Output:	(boolean)
    public function addLocation($p_iReference, $p_sZipcode, $p_sStreet = '', $p_sCountry = '') {
      if(strlen($p_sCountry) == 2) {
        $sCountry = $p_sCountry;
      }
      else {
        $sCountry = $this->getCountryByZipcode($p_sZipcode);
      }

      if($sCountry !== false) {
        $this->m_aLocations[] = ['country' => $sCountry, 'zipcode' => $p_sZipcode, 'street' => $p_sStreet];
      }
      else {
        //pd("Land niet gevonden: ".$p_sCountry." ".$p_sZipcode);
        return false;
      }
    }

    // getGeocode
    // - Public
    // Returns the geocodes for the added locations
    // Input:	-
    // Output:	array
    public function getGeocode() {
      if(count($this->m_aLocations) > 0) {
        $this->m_oGeocodeData = new clsGeocodeData();

        return $this->m_oGeocodeData->getGeocode($this->m_aLocations);
      }
      else {
        return false;
      }
    }

  }

?>