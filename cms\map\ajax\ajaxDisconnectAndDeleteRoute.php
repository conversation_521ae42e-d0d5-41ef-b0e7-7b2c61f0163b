<?php

  // http://rde-legacy.rde.localhost/cms/map/ajax/ajaxDisconnectAndDeleteRoute.php?routeId=20327

  require_once($_SERVER['DOCUMENT_ROOT'] . '/config/headercode_global.inc.php');
  $oAuth = new clsAuth();
  $oAuth->validate();

  include_once($_SERVER['DOCUMENT_ROOT'] . '/cms/framework/classes/clsMySQL.php');
  clsDBSingleton::instantiate();

  $routeId = $_GET['routeId'];

  if(is_numeric($routeId)) {

    //-- eerst even controlleren of de status lager is dan 60
    //-- ander ga je een bak/rek verwijderen en dat mag niet.

    $sQueryGpsRde = "SELECT
                                    quotationId
                                FROM
                                    rde_route.gpsbuddy_rde
                                WHERE
                                    routeId = '" . $routeId . "'
                                ";

    $oGpsRdeRouteId = clsDBSingleton::processMultiObjectQuery($sQueryGpsRde);
    $aGetLastStatus = [];

    foreach ($oGpsRdeRouteId as $keyQuoteArray => $valueQuoteArray) {
      $aGetLastStatus[] = $valueQuoteArray->quotationId;
    }

    //-- alle dubbele verwijderen
    $aGetLastStatus = array_unique($aGetLastStatus);
    //-- keys opnieuw indexeren
    $aGetLastStatus = array_values($aGetLastStatus);
    $aStatusIds = [];
    $aContainerIds = [];

    foreach ($aGetLastStatus as $keyLastStatus => $valueLastStatus) {

      $sqlStatusId = "SELECT statusId, sendMailToClient, callOrEmailNotes, quotationNumber, quotationVersion, dispatchAppointment FROM rde_b1mo.quotations WHERE quotationId = '" . $valueLastStatus . "'  LIMIT 1";
      $oStatusIdQuotation = clsDBSingleton::processSingleObjectQuery($sqlStatusId);
      $aStatusIds[] = $oStatusIdQuotation->statusId;
      //-- alleen iets verwijderen als de status lager dan 60 is
      //-- bakken en rek koppelingen mogen namelijk niet verwijderd worden.
      if($oStatusIdQuotation->statusId < '60') {
        include_once($_SERVER['DOCUMENT_ROOT'] . '/cms/crm/route/classes/clsCRMRouteRemoveRouteId.php');
        $oRemoveRouteId = new clsCRMRouteRemoveRouteId();
        $oRemoveRouteId->removeRouteId($valueLastStatus);

      }


    }

  }

  echo '{}';


