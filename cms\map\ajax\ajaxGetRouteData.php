<?php

  // https://rde-legacy.rde.localhost/cms/map34/ajax/ajaxGetRouteData.php?date=2018-12-17

  require_once($_SERVER['DOCUMENT_ROOT'] . '/config/headercode_global.inc.php');
  $oAuth = new clsAuth();
  $oAuth->validate();


  //aanroepen vanuit de andere domeinen
  $allowedOrigins = [
    "www.raamdorpel.nl",
    "api.raamdorpel.nl",
    "beheer.raamdorpel.nl",
    "rde-legacy.rde.localhost",
    "example.com.local",
    "gsdprojects.klapjap.nl",
  ];

  if(isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
    $httpString = 'https://';
  }
  else {
    $httpString = 'http://';
  }

  if(in_array($_SERVER["HTTP_HOST"], $allowedOrigins)) {
    header("Access-Control-Allow-Origin: " . $httpString . $_SERVER["HTTP_HOST"]);
  }

  include_once($_SERVER['DOCUMENT_ROOT'] . '/cms/framework/classes/clsMySQL.php');
  clsDBSingleton::instantiate();
  $routeDate = $_GET['date'];
  $aRouteDate = explode("-", $routeDate);

  include_once($_SERVER['DOCUMENT_ROOT'] . '/cms/crm/classes/clsStatus.php');
  $oStatusData = new clsStatus();

  //-- moet zijn gezet en een getal zijn
  if(isset($aRouteDate[0]) && isset($aRouteDate[1]) && isset($aRouteDate[2]) && is_numeric($aRouteDate[0]) && is_numeric($aRouteDate[1]) && is_numeric($aRouteDate[2])) {

    $sSqlRoutes = "SELECT
                                r.subject,
                                r.notes,
                                r.domestic,
                                r.routeId,
                                r.truckId,
                                r.mapExtraPoi,
                                r.poiAddresId,
                                r.rank,
                                t.name,
                                t.orderid
                            FROM
                                rde_route.gpsbuddy_routes r
                            LEFT JOIN
                                rde_route.gpsbuddy_trucks t
                            ON
                                r.truckId = t.truckId
                            WHERE
                                r.date = '" . $routeDate . "'
                            ORDER BY
                                t.orderid ASC,
                                r.truckId ASC,
                                r.rank ASC,
                                r.routeId ASC 
                                
                            ";

    $oRoutes = clsDBSingleton::processMultiObjectQuery($sSqlRoutes);

    if($oRoutes !== false) {

      include_once($_SERVER['DOCUMENT_ROOT'] . '/cms/crm/route/classes/clsCRMRouteGetAddress.php');
      $oCRMRouteGetAddress = new clsCRMRouteGetAddress();

      $routeTransport = [];

      //-- array aanpassen zodat hij goed ingelezen kan worden.
      foreach ($oRoutes as $keyRoutes => $valueRoutes) {

        if($valueRoutes->mapExtraPoi === "1") {

          //-- bij een mapExtraPoi zijn heel veel waardes niet beschikbaar omdat
          //-- dit puur om een lokatie gaat
          // $valueRoutes->poiAddresId

          $sqlAddresses = "SELECT
                                        street, nr, extension, zipcode, domestic, country, longitude, latitude
                                    FROM
                                        rde_cms.crm_addresses
                                    WHERE
                                        addressId = '" . $valueRoutes->poiAddresId . "'
                                    LIMIT 1
                                    ";

          $oAddressData = clsDBSingleton::processSingleObjectQuery($sqlAddresses);

          $oRouteAddressData = new stdClass;
          $oRouteAddressData->street = $oAddressData->street;
          $oRouteAddressData->nr = $oAddressData->nr;
          $oRouteAddressData->extension = $oAddressData->extension;
          $oRouteAddressData->zipcode = $oAddressData->zipcode;
          $oRouteAddressData->domestic = $oAddressData->domestic;
          $oRouteAddressData->country = $oAddressData->country;
          $oRouteAddressData->longitude = $oAddressData->longitude;
          $oRouteAddressData->latitude = $oAddressData->latitude;
          $oRouteAddressData->addressDeliveryId = $valueRoutes->poiAddresId;


        }
        else {

          $sqlRoute = "SELECT quotationId FROM rde_route.gpsbuddy_rde
                                      WHERE routeId = '" . $valueRoutes->routeId . "' AND quotationId != 0 LIMIT 1";
          $oSqlRouteQuoteId = clsDBSingleton::processSingleObjectQuery($sqlRoute);
          $oRouteAddressData = $oCRMRouteGetAddress->getRouteAddressData($oSqlRouteQuoteId->quotationId);

        }

        //----------------------------------
        if(isset($routeTransport[$valueRoutes->name]['counter'])) {
          $routeTransport[$valueRoutes->name]['counter'] = $routeTransport[$valueRoutes->name]['counter'] + 1;
        }
        else {
          $routeTransport[$valueRoutes->name]['counter'] = 0;
        }
        $routeTransportKey = $routeTransport[$valueRoutes->name]['counter'];
        //----------------------------------
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['addressDeliveryId'] = $oRouteAddressData->addressDeliveryId;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['orderId'] = $keyRoutes;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['longitude'] = $oRouteAddressData->longitude;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['latitude'] = $oRouteAddressData->latitude;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['street'] = $oRouteAddressData->street;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['nr'] = $oRouteAddressData->nr;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['extension'] = $oRouteAddressData->extension;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['routeId'] = $valueRoutes->routeId;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['subject'] = $valueRoutes->subject;
        $notes = trim(preg_replace('/\s\s+/', ' ', $valueRoutes->notes));
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['notes'] = $valueRoutes->notes;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['domestic'] = $valueRoutes->domestic;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['rank'] = $valueRoutes->rank;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['truckId'] = $valueRoutes->truckId;
        //----------------------------------

        //-----------------------------------------------------------------------------
        //-----------------------------------------------------------------------------
        //-----------------------------------------------------------------------------

        //-- we willen ook de laagste status Id gaan toevoegen
        //-- en we willen de info hebben van de route
        //-- en bellen
        //-- en link naar juiste route overzicht voor die dag en transport

        $sQueryGpsRde = "SELECT
                                            quotationId
                                        FROM
                                            rde_route.gpsbuddy_rde
                                        WHERE
                                            routeId = '" . $valueRoutes->routeId . "'
                                        ";

        $oGpsRdeRouteId = clsDBSingleton::processMultiObjectQuery($sQueryGpsRde);
        $aGetLastStatus = [];

        foreach ($oGpsRdeRouteId as $keyQuoteArray => $valueQuoteArray) {

          //-- check and show status id
          // $valueQuoteArray->quotationId
          $sqlStatusId = "SELECT statusId FROM rde_b1mo.quotations WHERE quotationId = '" . $valueLastStatus . "'  LIMIT 1";
          $oStatusIdQuotation = clsDBSingleton::processSingleObjectQuery($sqlStatusId);
          $aStatusIds[] = $oStatusIdQuotation->statusId;
          $aGetLastStatus[] = $valueQuoteArray->quotationId;

        }

        //-- alle dubbele verwijderen
        $aGetLastStatus = array_unique($aGetLastStatus);
        //-- keys opnieuw indexeren
        $aGetLastStatus = array_values($aGetLastStatus);
        $aStatusIds = [];
        $aContainerIds = [];

        $sendMailToClient = '';
        $quoteIds = '';

        $lastQuotationId = '';
        $lastContainerId = '';
        $sCallOrEmailNotes = '';
        $sCallOrEmailNotesQuotation = '';
        $sMeters = 0;
        $sMetersUnder40 = 0;

        $containerIdArray = [];

        foreach ($aGetLastStatus as $keyLastStatus => $valueLastStatus) {

          $sqlStatusId = "SELECT meters, statusId, sendMailToClient, callOrEmailNotes, quotationNumber, quotationVersion, dispatchAppointment, callOrEmailNotes FROM rde_b1mo.quotations WHERE quotationId = '" . $valueLastStatus . "'  LIMIT 1";
          $oStatusIdQuotation = clsDBSingleton::processSingleObjectQuery($sqlStatusId);
          $aStatusIds[] = $oStatusIdQuotation->statusId;
          $sqlContainerId = "SELECT containerId FROM rde_b1mo.containers_quotations WHERE quotationId = '" . $valueLastStatus . "'";
          $rContainerId = clsDBSingleton::processMultiObjectQuery($sqlContainerId);

          foreach ($rContainerId as $valueContainerId => $keyContainerId) {

            $aContainerIds[$valueLastStatus][] = $keyContainerId->containerId;
            $sqlContainerType = "SELECT type FROM rde_b1mo.containers WHERE containerId = '" . $keyContainerId->containerId . "' LIMIT 1";

            $oContainerType = clsDBSingleton::processSingleObjectQuery($sqlContainerType);
            $containerIdArray[$valueRoutes->routeId][$keyContainerId->containerId] = $oContainerType->type;

            if($oContainerType->type === 'bak') {
              $bakContainerAmount++;
            }
            elseif($oContainerType->type === 'rek') {
              $rekContainerAmount++;
            }
            elseif($oContainerType->type === 'pal') {
              $palContainerAmount++;
            }
            elseif($oContainerType->type === 'opr') {
              $oprContainerAmount++;
            }

          }

          if($oStatusIdQuotation->sendMailToClient === '1') {
            $sendMailToClient = '1';
          }

          if($oStatusIdQuotation->dispatchAppointment != '') {
            $quoteIds .= $oStatusIdQuotation->dispatchAppointment . ' - ';
          }

          if($oStatusIdQuotation->callOrEmailNotes != '') {
            $sCallOrEmailNotes .= $oStatusIdQuotation->callOrEmailNotes . ' - ';
            $sCallOrEmailNotesQuotation = $valueLastStatus;
          }

          $lastQuotationId = $valueLastStatus;
          $lastContainerId = $keyContainerId->containerId;
          $sMeters += $oStatusIdQuotation->meters;

          if($oStatusIdQuotation->statusId < 40) {
            $sMetersUnder40 += $oStatusIdQuotation->meters;
          }


        }

        $bakContainerAmount = 0;
        $rekContainerAmount = 0;
        $palContainerAmount = 0;
        $oprContainerAmount = 0;

        foreach ($containerIdArray as $keyQuote => $valueQuote) {

          foreach ($valueQuote as $keyContainer => $valueContainer) {

            if($valueContainer === 'bak') {
              $bakContainerAmount++;
            }
            elseif($valueContainer === 'rek') {
              $rekContainerAmount++;
            }
            elseif($valueContainer === 'pal') {
              $palContainerAmount++;
            }
            elseif($valueContainer === 'opr') {
              $oprContainerAmount++;
            }

          }

        }

        //-- haal de zipcode, country en street op zodat je op de marker kan
        //-- klikken. Er moet wel een marker eronder zitten. Anders gaat het niet werken.

        if($valueRoutes->mapExtraPoi === "1") {

          $oDataAddress = clsDBSingleton::dbcSelectOne('rde_cms.crm_addresses', 'addressId', $valueRoutes->poiAddresId, '*');
          $geoZipcode = str_replace(" ", "", $oDataAddress->zipcode);
          $geoCountry = $oDataAddress->country;
          $geoStreet = $oDataAddress->street . ' ' . $oDataAddress->nr . ' ' . $oDataAddress->extension;
          $geoStreet = urlencode($geoStreet);

        }
        else {

          $oDataQuotationExtra = clsDBSingleton::dbcSelectOne('rde_b1mo.quotations_extra', 'quotationId', $lastQuotationId, '*');
          $oDataAddress = clsDBSingleton::dbcSelectOne('rde_cms.crm_addresses', 'addressId', $oDataQuotationExtra->addressDeliveryId, '*');
          $geoZipcode = str_replace(" ", "", $oDataAddress->zipcode);
          $geoCountry = $oDataAddress->country;
          $geoStreet = $oDataAddress->street . ' ' . $oDataAddress->nr . ' ' . $oDataAddress->extension;
          $geoStreet = urlencode($geoStreet);

        }


        $image = '';
        if(!empty($aStatusIds)) {
          asort($aStatusIds);
          $aStatusIds = array_values($aStatusIds);
          $lowestStatus = min($aStatusIds);
          $oStatus = $oStatusData->getStatusDataById($lowestStatus);
          $image = $oStatus->imageName;
        }

        $oDataEmailToClients = null;
        if($valueRoutes->mapExtraPoi !== "1") {
          //-- kijk welke email zijn verzonden voor deze offerte
          $oDataEmailToClients = clsDBSingleton::dbcSelectOne('rde_b1mo.email_to_clients', 'routeId', $valueRoutes->routeId, '*');
        }

        //-----------------------------------------------------------------------------
        //-----------------------------------------------------------------------------
        //-----------------------------------------------------------------------------

        //-- dit is het resultaat van de query's hierboven.
        //-- de laagste statusId

        if($oDataEmailToClients !== null && $oDataEmailToClients !== '') {

          if($oDataEmailToClients->delivery === '1') {
            $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['emailsentdelivery'] = $oDataEmailToClients->delivery;
          }
          else {
            $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['emailsentdelivery'] = 0;
          }

          if($oDataEmailToClients->rackPickup === '1') {
            $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['emailsentrackpickup'] = $oDataEmailToClients->rackPickup;
          }
          else {
            $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['emailsentrackpickup'] = 0;
          }

          if($oDataEmailToClients->removeFromRoute === '1') {
            $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['emailsentremovefromroute'] = $oDataEmailToClients->removeFromRoute;
          }
          else {
            $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['emailsentremovefromroute'] = 0;
          }

        }
        else {

          $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['emailsentdelivery'] = 0;
          $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['emailsentrackpickup'] = 0;
          $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['emailsentremovefromroute'] = 0;

        }

        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['lowestStatusId'] = $lowestStatus;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['sendMailToClient'] = $sendMailToClient;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['quoteIds'] = $quoteIds;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['statusImageName'] = $image;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['lastQuotationId'] = $lastQuotationId;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['geoZipcode'] = $geoZipcode;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['geoCountry'] = $geoCountry;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['geoStreet'] = $geoStreet;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['sMeters'] = $sMeters;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['sMetersUnder40'] = $sMetersUnder40;
        $sqlContainerId = "SELECT containerNumber, type FROM rde_b1mo.containers WHERE containerId = '" . $lastContainerId . "' LIMIT 1";
        $oContainerId = clsDBSingleton::processSingleObjectQuery($sqlContainerId);
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['lastContainerNumber'] = $oContainerId->containerNumber;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['typeContainer'] = $oContainerId->type;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['callOrEmailNotes'] = $sCallOrEmailNotes;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['callOrEmailNotesQuotation'] = $sCallOrEmailNotesQuotation;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['bakAmount'] = $bakContainerAmount;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['rekAmount'] = $rekContainerAmount;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['palAmount'] = $palContainerAmount;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['oprAmount'] = $oprContainerAmount;
        $routeTransport[$valueRoutes->name]['values'][$routeTransportKey]['mapExtraPoi'] = $valueRoutes->mapExtraPoi;

        //-----------------------------------------------------------------------------
        //-----------------------------------------------------------------------------
        //-----------------------------------------------------------------------------

      }

      $jsonData = '{' . "\n";

      $counterTransport = 0;
      //-- main array routeData
      $jsonData .= '"truckid": {' . "\n";

      $counterTransport = 0;

      foreach ($routeTransport as $keyRouteTransp => $valueRouteTransp) {

        $jsonData .= "\n" . '"' . $keyRouteTransp . '":[' . "\n";

        //-- door transport volvo en iveco heen loopen
        $counterTransport++;
        foreach ($valueRouteTransp as $keyRT => $valueAddressInfo) {

          $counter = 0;
          //-- alle data per order
          if(!is_array($valueAddressInfo))
            continue; //ROBERT - warning afgevangen. volgens mij klopt deze code niet, $valueAddressInfo = 0
          foreach ($valueAddressInfo as $keyAI => $valueAI) {

            $jsonData .= '{"addressDeliveryId":"' . $valueAI['addressDeliveryId'] . '",' . "\n";
            $jsonData .= '   "orderId":"' . $valueAI['orderId'] . '",' . "\n";
            $jsonData .= '   "longitude":"' . $valueAI['longitude'] . '",' . "\n";
            $jsonData .= '   "latitude":"' . $valueAI['latitude'] . '",' . "\n";
            $jsonData .= '   "street":"' . $valueAI['street'] . '",' . "\n";
            $jsonData .= '   "nr":"' . $valueAI['nr'] . '",' . "\n";
            $jsonData .= '   "extension":"' . $valueAI['extension'] . '",' . "\n";
            $jsonData .= '   "routeId":"' . $valueAI['routeId'] . '",' . "\n";
            $jsonData .= '   "subject":"' . $valueAI['subject'] . '",' . "\n";
            $notes = trim(preg_replace('/\s\s+/', ' ', $valueAI['notes']));
            $jsonData .= '   "notes":"' . $notes . '",' . "\n";
            $jsonData .= '   "domestic":"' . $valueAI['domestic'] . '",' . "\n";
            $jsonData .= '   "rank":"' . $valueAI['rank'] . '",' . "\n";
            $jsonData .= '   "lowestStatusId":"' . $valueAI['lowestStatusId'] . '",' . "\n";
            $jsonData .= '   "emailsentdelivery":"' . $valueAI['emailsentdelivery'] . '",' . "\n";
            $jsonData .= '   "emailsentrackpickup":"' . $valueAI['emailsentrackpickup'] . '",' . "\n";
            $jsonData .= '   "emailsentremovefromroute":"' . $valueAI['emailsentremovefromroute'] . '",' . "\n";
            $jsonData .= '   "sendMailToClient":"' . $valueAI['sendMailToClient'] . '",' . "\n";
            $quoteIds = trim(preg_replace('/\s\s+/', ' ', $valueAI['quoteIds']));
            $jsonData .= '   "quoteIds":"' . $quoteIds . '",' . "\n";
            $callOrEmailNotes = trim(preg_replace('/\s\s+/', ' ', $valueAI['callOrEmailNotes']));
            $jsonData .= '   "callOrEmailNotes":"' . $callOrEmailNotes . '",' . "\n";
            $jsonData .= '   "statusImageName":"' . $valueAI['statusImageName'] . '",' . "\n";
            $jsonData .= '   "lastQuotationId":"' . $valueAI['lastQuotationId'] . '",' . "\n";
            $jsonData .= '   "lastContainerNumber":"' . $valueAI['lastContainerNumber'] . '",' . "\n";
            $jsonData .= '   "callOrEmailNotesQuotation":"' . $valueAI['callOrEmailNotesQuotation'] . '",' . "\n";
            $jsonData .= '   "geoZipcode":"' . $valueAI['geoZipcode'] . '",' . "\n";
            $jsonData .= '   "geoCountry":"' . $valueAI['geoCountry'] . '",' . "\n";
            $jsonData .= '   "geoStreet":"' . $valueAI['geoStreet'] . '",' . "\n";
            $jsonData .= '   "sMeters":"' . $valueAI['sMeters'] . '",' . "\n";
            $jsonData .= '   "sMetersUnder40":"' . $valueAI['sMetersUnder40'] . '",' . "\n";
            $jsonData .= '   "bakAmount":"' . $valueAI['bakAmount'] . '",' . "\n";
            $jsonData .= '   "rekAmount":"' . $valueAI['rekAmount'] . '",' . "\n";
            $jsonData .= '   "palAmount":"' . $valueAI['palAmount'] . '",' . "\n";
            $jsonData .= '   "oprAmount":"' . $valueAI['oprAmount'] . '",' . "\n";
            $jsonData .= '   "mapExtraPoi":"' . $valueAI['mapExtraPoi'] . '",' . "\n";
            $jsonData .= '   "typeContainer":"' . $valueAI['typeContainer'] . '",' . "\n";
            $jsonData .= '   "truckId":"' . $valueAI['truckId'] . '"}';
            $jsonData .= "\n";

            $counter++;
            if(count($valueAddressInfo) === $counter) {
              //-- einde van de loop

              if(count($routeTransport) === $counterTransport) {
                $jsonData .= ']';
              }
              else {
                $jsonData .= '],';
              }

            }
            else {
              $jsonData .= ',';
            }
          }
        }
      }

      $jsonData .= '}}' . "\n";


    }
    else {

      //-- route bestaat niet

    }

    header('Content-Type: application/json');
    echo $jsonData;

  }
  else {

    echo "<p>Datum klopt niet</p>";

  }
	
