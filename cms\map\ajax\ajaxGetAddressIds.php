<?php
  require_once($_SERVER['DOCUMENT_ROOT'] . '/config/headercode_global.inc.php');
  $oAuth = new clsAuth();
  $oAuth->validate();

  include_once($_SERVER['DOCUMENT_ROOT'] . '/cms/framework/classes/clsMySQL.php');
  clsDBSingleton::instantiate();

  //-- code here
  $country = $_GET['country'];
  $zipcode = str_replace(" ", "", $_GET['zipcode']);
  $nr = $_GET['nr'];
  $domestic = $_GET['domestic'];

  $sql = "SELECT addressId ";
  $sql .= " FROM rde_cms.crm_addresses ";
  $sql .= " WHERE country = '" . $country . "' ";
  $sql .= " AND zipcode = '" . $zipcode . "' ";
  $sql .= " AND nr = '" . $nr . "' ";
  $sql .= " AND domestic = '" . $domestic . "' ";
  $sql .= " AND mapExtra = '1' ";

  $oAddressID = clsDBSingleton::processMultiObjectQuery($sql);

  $jsonData = '{' . "\n";

  $counterTransport = 0;
  //-- main array routeData
  $jsonData .= '"addressIds": [ ' . "\n";
  $counter = 0;
  foreach ($oAddressID as $key => $value) {
    $jsonData .= '{"addressDeliveryId":"' . $value->addressId . '" }' . "\n";
    $counter++;
    if(count($oAddressID) !== $counter) {
      //-- niet de einde van de loop
      $jsonData .= ',';
    }

  }
  $jsonData .= '] }' . "\n";

  echo $jsonData;
