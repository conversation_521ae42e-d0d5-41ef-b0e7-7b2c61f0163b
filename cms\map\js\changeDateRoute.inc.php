<script type="text/javascript">

    function unique(array){
        return array.filter(function(el, index, arr) {
            return index == arr.indexOf(el);
        });
    }

    function makeRequestPromisses (method, url, done) {
        return new Promise(function (resolve, reject) {
            var xhr = new XMLHttpRequest();
            xhr.open(method, url);
            xhr.onload = function () {
                if (this.status >= 200 && this.status < 300) {
                    resolve(xhr.response);
                } else {
                    reject({
                        status: this.status,
                        statusText: xhr.statusText
                    });
                }
            };
            xhr.onerror = function () {
                reject({
                    status: this.status,
                    statusText: xhr.statusText
                });
            };
            xhr.send();
        });
    }

    function checkForRemovalError(routeId, domestic, subject, nonRemovalErrorFlag) {

        var nameDLV = '#emailDelivery_' + routeId;
        var namePICK = '#emailPickup_' + routeId;
        var nameREM = '#emailRemove_' + routeId;
        var nonRemovalError = '';
        if ($(nameDLV).length > 0 && $(nameREM).length == 0) {
            var nonRemovalError = domestic + ' ' + subject + '\n\n';
            nonRemovalError += "Deze aflever route mag je nog niet verwijderen," +
                " omdat deze aflever route " +
                "ingeplanned staat en er is al een mail naar de klant gestuurd." +
                " Stuur eerst een verwijder mail om deze route te kunnen " +
                "verwijderen.";
            nonRemovalErrorFlag = true;
        }
        if ($(namePICK).length > 0 && $(nameREM).length == 0) {
            var nonRemovalError = domestic + ' ' + subject + '\n\n';
            nonRemovalError += "Deze pickup route mag je nog niet verwijderen," +
                " omdat deze ophaal route " +
                "ingeplanned staat en er is al een mail naar de klant gestuurd." +
                " Stuur eerst een verwijder mail om deze route te kunnen " +
                "verwijderen.";
            nonRemovalErrorFlag = true;
        }
        if (nonRemovalError !== '') {
            alert(nonRemovalError);
        }
        return nonRemovalErrorFlag;

    }

    /**
     *
     * @param transportId
     * @param availableDates
     */
    function changeDateRoute(transportId, availableDates) {

        var changeDate = $('#fldChangeDateRoute'+transportId).val();

        if (changeDate === '') {

            alert("U heeft nog geen datum gekozen");

        } else {

            //-- ajax move route data
            var changeDateOld = $('#fldRoutePlanningDate').val();
            var aChangeDateOldSplit = changeDateOld.split("-");
            var checkDateOld = aChangeDateOldSplit[2] +'-'+ aChangeDateOldSplit[1] +'-'+ aChangeDateOldSplit[0];

            //----------------------------------------------------------------------------------------------------------
            //-- part 1

            //-- format 2018-12-17
            makeRequestPromisses('GET', 'https://<?php echo $_SERVER['HTTP_HOST']; ?>/cms/map/ajax/ajaxGetRouteData.php?date=' + checkDateOld)
            .then(function (routeDateValues) {

                var nonRemovalErrorFlag = false;

                var routeDateValues = JSON.parse(routeDateValues);

                for (var key in routeDateValues.truckid) {
                    for (var key2 in routeDateValues.truckid[key]) {
                        if (key === 'Volvo' && transportId === 2) {
                            nonRemovalErrorFlag =
                                checkForRemovalError(routeDateValues.truckid[key][key2]['routeId'],
                                routeDateValues.truckid[key][key2]['domestic'],
                                routeDateValues.truckid[key][key2]['subject'],
                                nonRemovalErrorFlag);
                        }
                        if (key === 'Iveco' && transportId === 1) {
                            nonRemovalErrorFlag =
                                checkForRemovalError(routeDateValues.truckid[key][key2]['routeId'],
                                    routeDateValues.truckid[key][key2]['domestic'],
                                    routeDateValues.truckid[key][key2]['subject'],
                                    nonRemovalErrorFlag);
                        }
                    }
                }

                if (nonRemovalErrorFlag === true) {
                    throw new Error('Route verplaatsen is geannuleerd vanwege nog niet verzonden verwijder email.');
                } else {
                    return true;
                }


            })
            .catch(function (err) {
                console.error('error 1 message!', err.message);
                return false;
            })

            //----------------------------------------------------------------------------------------------------------
            //-- part 2
            // makeRequestPromisses('GET', 'https://<?php echo $_SERVER['HTTP_HOST']; ?>/cms/map/ajax/ajaxGetEmpty.php')
            .then(function (returnValue) {

                if (returnValue !== false) {

                    var changeDate = $('#fldChangeDateRoute'+transportId).val();
                    var aChangeDate = changeDate.split("-");
                    //-- eerst een controle of alles mails verstuur zijn
                    //-- routes ophalen van de te verhuizen
                    if (confirm('Weet je zeker dat je deze route wilt wijzigen?')) {
                        var newDateToUpdate = aChangeDate[1] +'-'+ aChangeDate[0] +'-'+ aChangeDate[2];
                        var changeDate = $('#fldChangeDateRoute'+transportId).val();
                        var changeDateOld = $('#fldRoutePlanningDate').val();
                        var aChangeDate = changeDate.split("-");
                        var aChangeDateOld = changeDateOld.split("-");
                        if (aChangeDate[2].length !== 4 || aChangeDate[2] === '0000') {
                            alert('Change date is ongeldig.');
                        } else {
                            var newChangeDate = aChangeDate[2] +'-'+ aChangeDate[1] +'-'+ aChangeDate[0];
                            var newChangeDateOld = aChangeDateOld[2] +'-'+ aChangeDateOld[1] +'-'+ aChangeDateOld[0];
                            if (aChangeDateOld[2].length !== 4 || aChangeDateOld[2] === '0000') {
                                alert('newChangeDateold date is ongeldig.');
                            } else {
                                //-- deze moet ik aanmaken
                                var stringQuery = 'https://<?php echo $_SERVER['HTTP_HOST']; ?>/cms/map/ajax/ajaxUpdateDateRoute.php?transportId=' + transportId + '&date=' + newChangeDate + '&olddate=' + newChangeDateOld;
                                // console.log('update gelukt!!! nieuwe datum is ingesteld.');
                                makeRequestPromisses('GET', stringQuery);
                            }
                        }
                    } else {
                        throw new Error('Route verplaatsen is gannuleerd');
                    }
                    return aChangeDate, newDateToUpdate;
                }

            })
            .catch(function (err) {
                console.error('error 2 message!', err.message);
            })

            //----------------------------------------------------------------------------------------------------------
            //-- part 3
            .then(function (newDateToUpdate) {

                if (newDateToUpdate !== undefined) {

                    console.log('newDateToUpdate: ' + newDateToUpdate);
                    var aChangeDate = newDateToUpdate.split("-");
                    //-- huidige datum instellen
                    var newDateToUpdateDayFirst = aChangeDate[1] +'-'+ aChangeDate[0] +'-'+ aChangeDate[2];
                    $('#fldRoutePlanningDate').val(newDateToUpdateDayFirst);

                    console.log('aChangeDate[2]: ' + aChangeDate[2]);
                    console.log('aChangeDate[1]: ' + aChangeDate[1]);
                    console.log('aChangeDate[0]: ' + aChangeDate[0]);

                    if (aChangeDate[2].length === 4) {
                        //-- then switch
                        var newDutchDateJsFormated = aChangeDate[2] +'-'+ aChangeDate[0] +'-'+ aChangeDate[1];
                    } else {
                        // waarschijlijk een geldige
                        var newDutchDateJsFormated = aChangeDate[1] +'-'+ aChangeDate[0] +'-'+ aChangeDate[2];
                    }

                    console.log('newDutchDateJsFormated: ' + newDutchDateJsFormated);
                    //-- dit update de rechter gedeelte.
                    // format 2018-12-17
                    updateRouteOverview(newDutchDateJsFormated, function() {
                        console.log('1 Finished updateRouteOverview callback. 1 inside updateRouteData');
                        console.log('newDutchDateJsFormated: ' + newDutchDateJsFormated);
                    });

                    //--------------------------------------------------------------------------------------------------
                    //--  add new date to availableDates in calender
                    availableDates.push(aChangeDate[0] +'-'+ aChangeDate[1] +'-'+ aChangeDate[2]);
                    console.log(1);
                    //--------------------------------------------------------------------------------------------------

                    var newDatedateJs = newDateToUpdate.split("-");
                    var month = Number(newDatedateJs[0]);
                    //-- javascript telt de maand vanaf 0 omdat de maand namen in een array staan
                    month = month - 1;

                    //-- de javascript date object moet op een specefieke wijze alle parameters ontvangen.
                    var realDate = new Date(newDatedateJs[2], month, newDatedateJs[1], 12, 0, 0, 0);
                    $("#datepicker").datepicker('setDate', realDate);
                    $("#datepicker").datepicker({ defaultDate: realDate });
                    var dateValueForAjaxGetRouteData = aChangeDateOldSplit[2] + "-" + aChangeDateOldSplit[1] + "-" + aChangeDateOldSplit[0];
                    console.log('dateValueForAjaxGetRouteData: ' + dateValueForAjaxGetRouteData);

                    //--------------------------------------------------------------------------------------------------
                    //-- bijv: soms zit er nog volvo op dezelfde datum als iveco wordt verhuist.
                    dateValueForAjaxGetRouteData = 'https://<?php echo $_SERVER['HTTP_HOST']; ?>/cms/map/ajax/ajaxGetRouteData.php?date=' + dateValueForAjaxGetRouteData;
                    //--------------------------------------------------------------------------------------------------

                    return makeRequestPromisses('GET', dateValueForAjaxGetRouteData);

                } else {

                    console.log('stap 3 return false');
                    return false;

                }

            })
            .catch(function (err) {
                console.dir(err);
                console.error('error 3 message!', err.message);
            })

            //----------------------------------------------------------------------------------------------------------
            //-- part 4
            // makeRequestPromisses('GET', dateValueForAjaxGetRouteData)
            .then(function (routeData) {

                //-- als deze waarde false is dan worden de oude routes verwijderd.
                //-- routeData is false als er op annuleren wordt gedrukt.

                if (routeData !== false) {

                    //-- je moet de routeData weer omzetten naar een object
                    var routeData = JSON.parse(routeData);

                    //-- adress alleen verwijderen als er niets meetr in zit.
                    if (Object.keys(routeData.truckid).length === 0) {

                        //----------------------------------------------------------------------------------------------------------
                        //-- remove key block -- search and destroy!!!
                        //-- find current key --
                        // first check if there is a another transport route on this date.
                        // console.log('pas verwijderen als alles geregeld is en er geen waardes meer zijn.');
                        var fullaChangeDateOldSplit = aChangeDateOldSplit[1] + "-" + aChangeDateOldSplit[0] + "-" + aChangeDateOldSplit[2];
                        var currentKey = Object.keys(availableDates).find(key => availableDates[key] === aChangeDateOldSplit[1] + "-" + aChangeDateOldSplit[0] + "-" + aChangeDateOldSplit[2]);
                        if (currentKey !== undefined) {
                            //-- remove key
                            availableDates.splice(currentKey, 1);
                            // console.log('Key is removed: ' + fullaChangeDateOldSplit);
                        }
                        availableDates = unique(availableDates);
                        //-- remove key block --- search and destroy!!!
                        //----------------------------------------------------------------------------------------------------------

                    }

                    var newChosenDate = availableDates[(availableDates.length-1)];
                    var aNewChosenDate = newChosenDate.split("-");
                    //-- huidige datum instellen
                    var newDateToUpdateDayFirst = aNewChosenDate[1] +'-'+ aNewChosenDate[0] +'-'+ aNewChosenDate[2];

                    console.log('newDateToUpdateDayFirst: ' + newDateToUpdateDayFirst);
                    // format 2018-12-17 dit update de rechter gedeelte.
                    updateRouteOverview(newDateToUpdateDayFirst, function() {
                        console.log('2 Finished updateRouteOverview callback. 1 inside updateRouteData');
                    });

                    var month = Number(aNewChosenDate[0]);
                    //-- javascript telt de maand vanaf 0 omdat de maand namen in een array staan
                    month = month - 1;
                    var realDate = new Date(aNewChosenDate[2], month, aNewChosenDate[1], 12, 0, 0, 0);
                    $("#datepicker").datepicker('setDate', realDate);
                    $("#datepicker").datepicker({ defaultDate: realDate });

                    var newDateUpdate = aNewChosenDate[2] +'-'+ aNewChosenDate[0] +'-'+ aNewChosenDate[1];


                    console.log('newDateUpdate: ' + newDateUpdate);
                    // format 2018-12-17
                    //-- dit update de rechter gedeelte.
                    updateRouteOverview(newDateUpdate, function() {
                        console.log('3 Finished updateRouteOverview callback. 1 inside updateRouteData');
                    });


                }

            })
            .catch(function (err) {
                console.error('error 4 message!', err.message);
            });


        } // if (changeDate === '') {

    }

</script>