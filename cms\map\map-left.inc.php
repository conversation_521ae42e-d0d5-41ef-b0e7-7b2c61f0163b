<form name="hiddenLoginForm" method="post" action="https://frontoffice2.gps-buddy.com/en-US/Account/Login" target="_blank">
  <input type="hidden" name="userName" value="<EMAIL>"/>
  <input type="hidden" name="password" value="f7a44970bc8c453fb1a15659086c9b1d"/>
</form>

<form id="frmRDEMaps" method="POST" action="map.php">
  <p>

  <div id="mapButtonsTopLeftContainer">
    <input type="submit" class="rdebtn" name="btnSubmit" value="Ververs"/>
    <input type="button" class="rdebtn" name="btnVolvo" value="Volvo" onclick="javascript:seeVolvo();"/>
    <input type="button" class="rdebtn" name="btnIveco" value="Iveco" onclick="javascript:seeIveco();"/>
    <input type="button" class="rdebtn" name="btnBuddy" value="B" onclick="javascript:GPSBuddyLogin();"/>
  </div>

  <span onClick="javascriptuncheckall();" class="rdebtn">Deselecteer</span>

  <script type="text/javascript">

    $(function () {

      $("#fldDateRoute4weeks").datepicker({

        closeText: 'Sluiten',
        prevText: '<',
        nextText: '?',
        currentText: 'Vandaag',
        monthNames: ['januari', 'februari', 'maart', 'april', 'mei', 'juni',
          'juli', 'augustus', 'september', 'oktober', 'november', 'december'],
        monthNamesShort: ['jan', 'feb', 'maa', 'apr', 'mei', 'jun',
          'jul', 'aug', 'sep', 'okt', 'nov', 'dec'],
        dayNames: ['zondag', 'maandag', 'dinsdag', 'woensdag', 'donderdag', 'vrijdag', 'zaterdag'],
        dayNamesShort: ['zon', 'maa', 'din', 'woe', 'don', 'vri', 'zat'],
        dayNamesMin: ['zo', 'ma', 'di', 'wo', 'do', 'vr', 'za'],
        weekHeader: 'Wk',
        dateFormat: 'dd-mm-yy',
        firstDay: 1,
        isRTL: false,
        showMonthAfterYear: false

      });


    });

  </script>

  <?php

    if(isset($_POST['fldDateRoute4weeks']) && $_POST['fldDateRoute4weeks'] != '') {

      echo '<div id="fldDateRoute4weeksContainer">';
      echo '    <input type="text" name="fldDateRoute4weeks" class="changeDateRoute4Weeks" id="fldDateRoute4weeks" value="' . $_POST['fldDateRoute4weeks'] . '" />';
      echo '</div>';

    }
    else {

      $sTodayPlusFourWeeksLater = date('d-m-Y', strtotime(date("d-m-Y") . ' + 28 days'));

      echo '<div id="fldDateRoute4weeksContainer">';
      echo '    <input type="text" name="fldDateRoute4weeks" class="changeDateRoute4Weeks" id="fldDateRoute4weeks" value="' . $sTodayPlusFourWeeksLater . '" />';
      echo '</div>';

    }

  ?>

  <div class="seperationGreyLine"></div>

  </p>


  <div class="colorCheckboxesMap">

    <ul class="checkboxStatus">
      <?php

        $aStatus = [
          '20' => 'Opdracht',
          '30' => 'Voorbereid',
          '35' => 'Voorbereid Print',
          '38' => 'In productie',
          '40' => 'Geproduceerd',
          '50' => 'Verzendklaar',
          '53' => 'Geladen',
          '55' => 'Geleverd',
        ];

        $aIcon = [
          '20' => 'rd1.png',
          '30' => 'rd2.png',
          '35' => 'pp1.png',
          '38' => '38.png',
          '40' => 'rd3.png',
          '50' => 'rd4B.png',
          '53' => 'rd6.png',
          '55' => 'rd5.png',
        ];

        foreach ($aStatus as $sStatus => $sDescription) {

          $sChecked = ((isset($_POST['chkStatus']) && in_array($sStatus, $_POST['chkStatus'])) || $_SERVER['REQUEST_METHOD'] == 'GET') ? ' checked="checked"' : '';
          echo "<li><input type=\"checkbox\" name=\"chkStatus[]\" value=\"{$sStatus}\" id=\"chk_{$sStatus}\" {$sChecked}/> <label for=\"chk_{$sStatus}\"> <img src=\"icons/{$aIcon[$sStatus]}\" title='" . $sStatus . " - " . $sDescription . "' alt='" . $sStatus . " - " . $sDescription . "' /></label></li>\n";

        }

      ?>

    </ul>

    <ul class="checkboxStatusExtras">

      <li><input type="checkbox" name="chkContainers" value="1" <?php if($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['chkContainers']) && $_POST['chkContainers'] == 1) {
          echo 'checked="checked"';
        } ?> id="chk_containers"/> <label for="chk_containers"> Bakken &amp; Rekken</label></li>


      <li>
        <input type="checkbox" name="chkGpsBuddy" value="1"
          <?php
            if($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['chkGpsBuddy']) && $_POST['chkGpsBuddy'] == 1) {
              echo 'checked="checked"';
            }
            elseif($_SERVER['REQUEST_METHOD'] == 'POST' && !isset($_POST['chkGpsBuddy'])) {
              echo '';
            }
            else {
              // echo 'checked="checked"';
            }
          ?> id="chk_GpsBuddy"/> <label for="chk_GpsBuddy"> GpsBuddy</label>
      </li>

      <li>
        <input type="checkbox" name="chkPoi" value="1"
          <?php
            if($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['chkPoi']) && $_POST['chkPoi'] == 1) {
              echo 'checked="checked"';
            }
            elseif($_SERVER['REQUEST_METHOD'] == 'POST' && !isset($_POST['chkPoi'])) {
              echo '';
            }
            else {
              // echo 'checked="checked"';
            }
          ?> id="chk_Poi"/> <label for="chk_Poi">Nuttige punten</label>
      </li>

      <li>
        <input type="checkbox" name="chkSmallOrders" value="1" <?php if($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['chkSmallOrders']) && $_POST['chkSmallOrders'] == 1)  echo 'checked="checked"' ?> id="chk_small_orders"/>
        <label for="chk_small_orders"> Kleine Orders</label>
      </li>

      <li>
        <label for="chkBrandChinese">
          <input type="checkbox" name="chkBrandChinese" value="1" <?php if($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['chkBrandChinese']) && $_POST['chkBrandChinese'] == 1)  echo 'checked="checked"' ?> id="chkBrandChinese"/>
           Chinees Natuursteen
        </label>
      </li>

      <li>
        <input type="checkbox" name="chkBelgischArduin" value="1" <?php if($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['chkBelgischArduin']) && $_POST['chkBelgischArduin'] == 1)  echo 'checked="checked"' ?> id="chkBelgischArduin"/>
        <label for="chkBelgischArduin"> Belgisch Arduin / Zimbabwe</label>
      </li>


    </ul>

  </div>

  <?php

    $oMapData = new clsMapData();
    if($oMap->getNotFound() !== false && count($oMap->getNotFound())>0) {
      $oMap->showMissingOrders = true;
    }
    if($oMap->showMissingOrders === true) {

      if(count($oMap->getNoQuotationsExtra())>0) {
        echo "" . "<div id=\"containerList\">\n" . "	<p><b>De volgende order(s) hebben geen quote extra: </b></p>\n" . "	<ul>\n";
        foreach ($oMap->getNoQuotationsExtra() as $aValues) {

          // echo '<li>$aValues: '.$aValues.'</li>';

          $oMapData->quotationId = $aValues;
          $companyIdResult = $oMapData->getCompanyId();
          echo "		<li>
                                        <a href=\"/cms/crm/crm.php?tabadmin=";
          echo "delivery&quotationId={$aValues}\" target=\"_blank\">33 quotationId " . $aValues . "</a>
                                    </li>\n";
        }
        echo "</ul>\n";
        echo "</div>\n";
      }

      if(count($oMap->getCoordinatesDoNotExists())>0) {
        echo "" . "<div id=\"containerList\">\n" . "	<p><b>De volgende order(s) hebben geen co&ouml;rdinaten: </b></p>\n" . "	<ul>\n";
        foreach ($oMap->getCoordinatesDoNotExists() as $aValues) {

          echo '<script>';
          echo " console.log('\$aValues: " . $aValues . "'); ";
          echo '</script>';

          $oMapData->quotationId = $aValues;
          $companyIdResult = $oMapData->getCompanyId();
          echo "		<li>
                                    <a href=\"/cms/crm/crm.php?tabadmin=delivery&quotationId={$aValues}\" target=\"_blank\">quotationId:: " . $aValues . "</a>
                                </li>\n";

        }
        echo "</ul>\n";
        echo "</div>\n";
      }

      if($oMap->getNotFound() !== false) {
        echo "<div id=\"containerList\">\n" . "	<p><b>De volgende locatie(s) zijn niet gevonden:</b></p>\n" . "	<ul>\n";
        foreach ($oMap->getNotFound() as $aOrder) {
          $oMap->zipcode2 = $aOrder['geo_zipcode'];
          $oMap->nr = $aOrder['geo_nr'];
          $type = $oMap->getAddressType();
          if(isset($type[0])) {
            $type = $type[0]['type'];
          }
          else {
            $type = 'leeg';
          }
          echo '<li>';
          echo "<a href=\"/cms/crm/crm.php?tabadmin=delivery&quotationId={$aOrder['orderId']}\" target=\"_blank\">{$aOrder['geo_country']} {$aOrder['geo_zipcode']}</a>  ({$aOrder['orderCount']} orders)";
          if($type == 'visit' || $type == 'delivery') {
            $oMapData->quotationId = $aOrder['orderId'];
            $companyIdResult = $oMapData->getCompanyId();
            echo " <a href=\"/cms/crm/crm.php?tab=locations2&companyId={$companyIdResult}\" target=\"_blank\">Naar bedrijf</a>";
          }
          echo '</li>';

        }

        echo "	</ul>\n" . "</div>\n";
      }
      else {

        echo "<hr />\n";
        echo "<div id=\"containerList\">\n";
        echo "<p><b>De volgende locatie(s) zijn niet gevonden:</b></p>\n";
        echo "</div>\n";

      }

    } // if ($oMap->showMissingOrders === true) {

  ?>

  <div id="containerList"><p>Geen locatie geselecteerd</p></div>

  <div id="locationOrders"></div>

  <div id="locationPoi"></div>

</form>