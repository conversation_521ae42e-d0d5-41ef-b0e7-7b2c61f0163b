/**
 *
 * @param transportId
 */
function updateRouteData(transportId, dateValue, changeDateOld, callback) {

    $.ajax({
        type: 'GET',
        dataType: "json",
        data: {date: dateValue},
        url: "ajax/ajaxGetRouteData.php",
        success: function(data) {

            var nonRemovalErrorFlag = false;

            for (var key in data.truckid) {

                for (var key2 in data.truckid[key]) {

                    if (transportId === data.truckid[key][key2]['truckId']) {

                        var nameDLV = '#emailDelivery_' + data.truckid[key][key2]['routeId'];
                        var nameREM = '#emailRemove_' + data.truckid[key][key2]['routeId'];
                        var domestic = data.truckid[key][key2]['domestic'];
                        var subject = data.truckid[key][key2]['subject'];
                        var fullTitle = domestic + ' ' + subject + ':\n';

                        var nonRemovalError = '';
                        if ($(nameDLV).length > 0 && $(nameREM).length == 0) {
                            var nonRemovalError = fullTitle + "Deze route mag je nog niet verplaatsen," +
                                " omdat deze aflever route " +
                                "ingeplanned staat en er is al een mail naar de klant gestuurd." +
                                " Stuur eerst een verwijder mail om deze route te kunnen " +
                                "verwijderen.";
                            nonRemovalErrorFlag = true;
                        }

                        var namePICK = '#emailPickup_' + data.truckid[key][key2]['routeId'];
                        if ($(namePICK).length > 0 && $(nameREM).length == 0) {
                            var nonRemovalError = fullTitle + "Deze route mag je nog niet verplaatsen," +
                                " omdat deze ophaal route " +
                                "ingeplanned staat en er is al een mail naar de klant gestuurd." +
                                " Stuur eerst een verwijder mail om deze route te kunnen " +
                                "verwijderen.";
                            nonRemovalErrorFlag = true;
                        }

                        if (nonRemovalError !== '') {
                            alert(nonRemovalError);
                        }

                        var domestic = data.truckid[key][key2]['domestic'];

                    }


                }


            }

            if (nonRemovalErrorFlag === false) {
                if (callback && typeof(callback) === "function") {
                    callback();
                }
            }

        },
        error: function(jqXHR, exception) {
            console.log('error code yGpdokVP');
            if (jqXHR.status === 0) {
                console.log('Not connect.n Verify Network.');
                alert('Not connect.n Verify Network.');
            } else if (jqXHR.status == 404) {
                console.log('Requested page not found. [404]');
                alert('Requested page not found. [404]');
            } else if (jqXHR.status == 500) {
                console.log('Internal Server Error [500].');
                alert('Internal Server Error [500].');
            } else if (exception === 'parsererror') {
                console.log('Requested JSON parse failed. possible that its not well formatted.');
                alert('Requested JSON parse failed. possible that its not well formatted.');
            } else if (exception === 'timeout') {
                console.log('Time out error.');
                alert('Time out error.');
            } else if (exception === 'abort') {
                console.log('Ajax request aborted.');
                alert('Ajax request aborted.');
            } else {
                console.log('Uncaught Error.n' + jqXHR.responseText);
                alert('Uncaught Error.n' + jqXHR.responseText);
            }
        }

    }); // $.ajax({

} // function updateRouteOverview(dateValue) {
