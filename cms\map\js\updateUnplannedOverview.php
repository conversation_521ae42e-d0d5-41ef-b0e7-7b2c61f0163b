<script type="text/javascript">

    function updateUnplannedOverview(weekid, callback) {

        $.ajax({
            type: 'GET',
            dataType: "json",
            data: {weekid: weekid},
            url: "ajax/ajaxGetUnplannedData.php",
            success: function (data) {

                var alphabet = ['A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z'];
                alphabet.push('AA','AB','AC','AD','AE','AF','AG','AH','AI','AJ','AK','AL','AM','AN','AO','AP','AQ','AR','AS','AT','AU','AV','AW','AX','AY','AZ');
                alphabet.push('BA','BB','BC','BD','BE','BF','BG','BH','BI','BJ','BK','BL','BM','BN','BO','BP','BQ','BR','BS','BT','BU','BV','BW','BX','BY','BZ');

                var sQuotationList = '';

                for (var i=0;i<data.Error.length;i++) {
                    if (i === 0) {
                        sQuotationList += data.Error[i].quotationId;
                    } else {
                        sQuotationList += ', ' + data.Error[i].quotationId;
                    }
                }

                $("#sortableContainerWeek0").html('');

                for (var x=0;x<data.weekoverview[0][0].length;x++) {

                    var keyPlusOne = parseInt(x) + 1;

                    $("#sortableContainerWeek0").append('<li class="ui-state-default ui-sortable-handle" id="12353667"> '
                        +'<span id="sortableSpanAlphabet">'+alphabet[keyPlusOne]+'</span>'+' '
                        +'<span id="sortableSpanStatusImage"><img src="" /></span> ' + ' '
                        +'<span id="sortableSpanDomestic"><b>'+data.weekoverview[0][0][x].domestic+'</b></span>'+' '
                        +'<span id="sortableSpanSubject">'+data.weekoverview[0][0][x].subject+'</span> ' + ' '
                        +'<span id="sortableSpanSubjectUnder"></span> ' + ' '

                        + '<a style="float:right;margin-right:5px;" '
                        + 'href="#" style="text-decoration: underline;" '
                        + 'onclick="window.open(\'/cms/crm/crm.php?tabroute=route&quotationid='
                        + data.weekoverview[0][0][x].quotationId+'\',\'_blank\');">'
                        + '<img src="/cms/images/icons/delivery-truck.png" alt="route" ></img></a>'

                        +'<div style="clear: both;"></div>'
                        +'</li>');

                }

                $("#waypointInsideWeek0").html('');

                //-- hier staan de locatie gegevens in voor de markers en route
                //-- javascript.inc.php leest deze uit en maakt er markers van.
                for (var j=0;j<data.weekoverview[0][0].length;j++) {
                    $("#waypointInsideWeek0").append('<input type="hidden" class="wayptsclassGeoInfo0" name="bar" disabled="disabled" style="width:250px;" value="'+data.weekoverview[0][0][j].geoZipcode + ',' + data.weekoverview[0][0][j].geoCountry + ',' + data.weekoverview[0][0][j].geoStreet+'">');
                    $("#waypointInsideWeek0").append('<input type="hidden" class="wayptsclass0" name="bar" disabled="disabled" style="width:250px;" value="'+data.weekoverview[0][0][j].latitude + ',' + data.weekoverview[0][0][j].longitude+'">');
                }

                $("#waypointInsideWeek0").append('<input type="hidden" name="start" id="start" disabled="disabled" style="width:250px;" value="51.3581276, 5.20558">');
                $("#waypointInsideWeek0").append('<input type="hidden" name="end" id="end" disabled="disabled" style="width:250px;" value="51.3581276, 5.20558">');

                if (sQuotationList !== '') {
                    alert('De volgende quotationId\'s hebben geen addressDeliveryId in quotations_extra: ' + sQuotationList
                        + '\n\n Zonder deze id kan ik de locatie gegevens kan ik geen punt op de kaart zetten');
                }

                if (callback && typeof(callback) === "function") {
                    callback();
                }

            },
            error: function(jqXHR, exception) {
                console.log('error code d3d3dedh84774473erutrewfwef');
                if (jqXHR.status === 0) {
                    console.log('Not connect.n Verify Network.');
                    alert('Not connect.n Verify Network.');
                } else if (jqXHR.status == 404) {
                    console.log('Requested page not found. [404]');
                    alert('Requested page not found. [404]');
                } else if (jqXHR.status == 500) {
                    console.log('Internal Server Error [500].');
                    alert('Internal Server Error [500].');
                } else if (exception === 'parsererror') {
                    console.log('Requested JSON parse failed. possible that its not well formatted. code 349t8y344');
                    alert('Requested JSON parse failed. possible that its not well formatted. code 349t8y344');
                } else if (exception === 'timeout') {
                    console.log('Time out error.');
                    alert('Time out error.');
                } else if (exception === 'abort') {
                    console.log('Ajax request aborted.');
                    alert('Ajax request aborted.');
                } else {
                    console.log('Uncaught Error.n' + jqXHR.responseText);
                    alert('Uncaught Error.n' + jqXHR.responseText);
                }
            }
        });

    }

</script>