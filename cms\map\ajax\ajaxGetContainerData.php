<?php

  require_once($_SERVER['DOCUMENT_ROOT'] . '/config/headercode_global.inc.php');
  $oAuth = new clsAuth();
  $oAuth->validate();

  include_once($_SERVER['DOCUMENT_ROOT'] . '/cms/framework/classes/clsMySQL.php');
  clsDBSingleton::instantiate();

  $containerId = $_GET['id'];
  $quoteId = $_GET['quoteid'];

  if($containerId > 0 && is_numeric($containerId) && $quoteId > 0) {

    //-- Ik maak hier een toggle van. Als hij aan staat dan zetten we heb uit en andersom.
    //-- een update plaats laten vinden

    $sql = "SELECT * 
                  FROM rde_b1mo.containers_quotations 
                  WHERE containerId = '" . $containerId . "' 
                  AND quotationId = '" . $quoteId . "' 
                  LIMIT 1";

    $oContainerData1 = clsDBSingleton::processSingleObjectQuery($sql);

    if($oContainerData1->nextroute === 'false') {

      //-- update to true
      $sqlUpdateDateRoute = "UPDATE rde_b1mo.containers_quotations SET nextroute = 'true' 
                  WHERE containerId = '" . $containerId . "' AND quotationId = '" . $quoteId . "'  ";
      $result = clsDBSingleton::processUpdateOrInsertQuery($sqlUpdateDateRoute);

    }
    elseif($oContainerData1->nextroute === 'true') {

      //-- update to false
      $sqlUpdateDateRoute = "UPDATE rde_b1mo.containers_quotations SET nextroute = 'false' 
                  WHERE containerId = '" . $containerId . "' AND quotationId = '" . $quoteId . "'  ";
      $result = clsDBSingleton::processUpdateOrInsertQuery($sqlUpdateDateRoute);

    }

    $sql = "SELECT * 
                  FROM rde_b1mo.containers_quotations 
                  WHERE containerId = '" . $containerId . "' 
                  AND quotationId = '" . $quoteId . "' 
                  LIMIT 1";

    $oContainerData2 = clsDBSingleton::processSingleObjectQuery($sql);

    $jsonData = '{"addressData":[' . "\n";
    $jsonData .= '   {';
    $jsonData .= '"containerQuotationId":"' . $oContainerData2->containerQuotationId . '"';
    $jsonData .= ',' . "\n";
    $jsonData .= '"containerId":"' . $oContainerData2->containerId . '"';
    $jsonData .= ',' . "\n";
    $jsonData .= '"quotationId":"' . $oContainerData2->quotationId . '"';
    $jsonData .= ',' . "\n";
    $jsonData .= '"deliverDate":"' . $oContainerData2->deliverDate . '"';
    $jsonData .= ',' . "\n";
    $jsonData .= '"deliverUserId":"' . $oContainerData2->deliverUserId . '"';
    $jsonData .= ',' . "\n";
    $jsonData .= '"nextroute":"' . $oContainerData2->nextroute . '"';
    $jsonData .= ',' . "\n";
    $jsonData .= '"returnDate":"' . $oContainerData2->returnDate . '"';
    $jsonData .= ',' . "\n";
    $jsonData .= '"returnUserId":"' . $oContainerData2->returnUserId . '"';
    $jsonData .= "\n" . '} ]}';

    echo $jsonData;

  }
  else {

    echo 'error: q84f9k8hc7sc562';

  }

