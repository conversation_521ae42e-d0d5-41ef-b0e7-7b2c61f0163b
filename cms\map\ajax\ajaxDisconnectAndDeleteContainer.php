<?php

  // http://rde-legacy.rde.localhost/cms/map/ajax/ajaxDisconnectAndDeleteContainer.php?routeId=20737

  require_once($_SERVER['DOCUMENT_ROOT'] . '/config/headercode_global.inc.php');
  $oAuth = new clsAuth();
  $oAuth->validate();

  include_once($_SERVER['DOCUMENT_ROOT'] . '/cms/framework/classes/clsMySQL.php');
  clsDBSingleton::instantiate();

  $routeId = $_GET['routeId'];

  if(is_numeric($routeId)) {

    //------------------------------------------------------------------------------------------------------
    // verwijder alle quotations die zijn gekoppeld aan route id in tabel

    $sqlDelete = "DELETE FROM rde_b1mo.email_to_clients WHERE routeId = '" . $routeId . "';";
    $sqlError = 'error: XcQ887PL delete from email_to_clients';
    clsDBSingleton::queryDelete($sqlDelete, $sqlError);

    //------------------------------------------------------------------------------------------------------

    $sQueryDeleteRDE = "DELETE FROM rde_route.gpsbuddy_routes WHERE routeId = '" . $routeId . "' ";
    clsDBSingleton::queryDelete($sQueryDeleteRDE, 'error: x3IfK26E gpsbuddy_routes');

    $sQueryDeleteRDE = "DELETE FROM rde_route.gpsbuddy_rde WHERE routeId = '" . $routeId . "' ";
    clsDBSingleton::queryDelete($sQueryDeleteRDE, 'error: x3IfK26E gpsbuddy_routes');

  }

  echo '{}';

