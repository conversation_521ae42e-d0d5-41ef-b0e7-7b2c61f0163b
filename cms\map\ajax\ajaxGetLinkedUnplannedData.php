<?php

  // http://rde-legacy.rde.localhost/cms/map/ajax/ajaxGetLinkedUnplannedData.php?weekid=0
  require_once($_SERVER['DOCUMENT_ROOT'] . '/config/headercode_global.inc.php');
  $oAuth = new clsAuth();
  $oAuth->validate();

  $error = false;
  include_once($_SERVER['DOCUMENT_ROOT'] . '/cms/framework/classes/clsMySQL.php');
  clsDBSingleton::instantiate();
  include_once($_SERVER['DOCUMENT_ROOT'] . '/cms/crm/classes/clsStatus.php');
  $oStatusData = new clsStatus();
  $weekId = $_GET['weekid'];
  if($weekId === null || $weekId === '') {
    echo '<p>Er moet een weekid meegegeven worden.</p>';
    exit;
  }
  $weekArray = [];

  /**
   * @param $date
   * @return array
   */
  function getWeekArray($date) {

    $ts = strtotime($date);
    // find the year (ISO-8601 year number) and the current week
    $year = date('o', $ts);
    $week = date('W', $ts);

    // print week for the current date
    for ($i = 1; $i <= 7; $i++) {
      // timestamp from ISO week date format
      $ts = strtotime($year . 'W' . $week . $i);
      $weekArray[] = date("Y-m-d", $ts);
    }

    return $weekArray;

  }

  if($weekId === '0') {
    $date = date('Y-m-d');
    $weekArray = getWeekArray($date);
  }
  elseif($weekId === '1') {
    $date = date('Y-m-d', strtotime(date('Y-m-d') . ' +1 week'));
    $weekArray = getWeekArray($date);
  }
  elseif($weekId === '2') {
    $date = date('Y-m-d', strtotime(date('Y-m-d') . ' +2 week'));
    $weekArray = getWeekArray($date);
  }
  elseif($weekId === '3') {
    $date = date('Y-m-d', strtotime(date('Y-m-d') . ' +3 week'));
    $weekArray = getWeekArray($date);
  }
  elseif($weekId === '4') {
    $date = date('Y-m-d', strtotime(date('Y-m-d') . ' +4 week'));
    $weekArray = getWeekArray($date);
  }

  $sDaysThisChosenWeek = '';

  foreach ($weekArray as $key => $value) {
    if($key === 0) {
      $sDaysThisChosenWeek .= '"' . $value . '"';
    }
    else {
      $sDaysThisChosenWeek .= ',"' . $value . '"';
    }
  }

  if($sDaysThisChosenWeek !== '') {

    //-- ik moet hier quotations selecteren die wel een routeId hebben in week 0.

    //-- quotation that dont have a routeId
    $sSqlQuoteNoRouteId = 'SELECT q.companyId
                            FROM rde_b1mo.quotations q
                            LEFT JOIN rde_route.gpsbuddy_rde link_qr
                            ON link_qr.quotationId = q.quotationId
                            WHERE q.dueDate IN (' . $sDaysThisChosenWeek . ')
                            AND link_qr.routeId IS NOT NULL
                            AND q.statusId > 10
                            AND q.statusId < 55
                            AND q.companyId IS NOT NULL
                            GROUP BY q.companyId';

    $oQuoteNoRouteId = clsDBSingleton::processMultiObjectQuery($sSqlQuoteNoRouteId);

    $sQuotationsVanCompany = [];

    foreach ($oQuoteNoRouteId as $keyRoutes => $valueRoutes) {

      $sqlQuotations = 'SELECT q.quotationId, link_routes.date FROM rde_b1mo.quotations q 
                                LEFT JOIN rde_route.gpsbuddy_rde link_qr
                                ON link_qr.quotationId = q.quotationId
                                LEFT JOIN rde_route.gpsbuddy_routes link_routes
                                ON link_routes.routeId = link_qr.routeId
                                WHERE q.companyId = "' . $valueRoutes->companyId . '" 
                                AND q.statusId <= 50
                                AND q.statusId > 10  
                                AND link_routes.date IS NULL 
                                ORDER BY q.quotationId DESC';

      // clsDBSingleton::echotest($sqlQuotations,__FILE__,__LINE__);

      $oQuotations = clsDBSingleton::processMultiObjectQuery($sqlQuotations);

      foreach ($oQuotations as $keyQuotations => $valueQuotations) {
        array_push($sQuotationsVanCompany, $valueQuotations->quotationId);
      }

    }

    //-- alle dubbele verwijderen
    $sQuotationsVanCompany = array_unique($sQuotationsVanCompany);
    //-- keys opnieuw indexeren
    $sQuotationsVanCompany = array_values($sQuotationsVanCompany);

  }

  $bakContainerAmount = 0;
  $rekContainerAmount = 0;
  $palContainerAmount = 0;
  $oprContainerAmount = 0;
  $sMeters = 0;
  $sMetersUnder40 = 0;

  if($oQuoteNoRouteId !== false) {

    //-- eerst een array aanmaken van alle unieke companyId's

    include_once($_SERVER['DOCUMENT_ROOT'] . '/cms/map/classes/clsCRMRouteGetAddressForAjax.php');
    $oCRMRouteGetAddressForAjax = new clsCRMRouteGetAddressForAjax();
    $routeTransport = [];
    $aQuotationsWithoutAddressDeliveryId = [];

    foreach (array_keys($sQuotationsVanCompany) as $valueQuotationsVanCompany) {
      $oDataQuotationExtra = clsDBSingleton::dbcSelectOne('rde_b1mo.quotations_extra', 'quotationId', $sQuotationsVanCompany[$valueQuotationsVanCompany], '*');
      if($oDataQuotationExtra->addressDeliveryId === null) {
        $aQuotationsWithoutAddressDeliveryId[] = $oDataQuotationExtra->quotationId;
      }
    }

    $aAddressDeliveryId = [];

    foreach (array_keys($sQuotationsVanCompany) as $keyQuotationsVanCompany) {

      $quotationIdOfCompany = $sQuotationsVanCompany[$keyQuotationsVanCompany];

      //-----------------------------------------------------------------------------
      //-----------------------------------------------------------------------------
      //-----------------------------------------------------------------------------

      $oRouteAddressData = $oCRMRouteGetAddressForAjax->getRouteAddressDataForAjax($quotationIdOfCompany);

      if($oRouteAddressData !== false) {

        //-- controle die alleen addressDeliveryId toe laat als deze nog niet bestaat.
        if(!in_array($oRouteAddressData->addressDeliveryId, $aAddressDeliveryId)) {
          array_push($aAddressDeliveryId, $oRouteAddressData->addressDeliveryId);

          //-- deze address mag worden toegevoegd aan de transport array

          //--------------------------------------------------------------------------------------------------
          //--------------------------------------------------------------------------------------------------
          //--------------------------------------------------------------------------------------------------

          $oCompanyData = clsDBSingleton::dbcSelectOne('rde_cms.crm_companies', 'companyId', $valueRoutes->companyId, '*');
          $oDataAddress = clsDBSingleton::dbcSelectOne('rde_cms.crm_addresses', 'addressId', $oDataQuotationExtra->addressDeliveryId, '*');

          $sqlStatusId = "SELECT meters, statusId, sendMailToClient, callOrEmailNotes, quotationNumber, quotationVersion, dispatchAppointment, callOrEmailNotes FROM rde_b1mo.quotations WHERE quotationId = '" . $quotationIdOfCompany . "'  LIMIT 1";
          $oStatusIdQuotation = clsDBSingleton::processSingleObjectQuery($sqlStatusId);
          $aStatusIds[] = $oStatusIdQuotation->statusId;
          $sqlContainerId = "SELECT containerId FROM rde_b1mo.containers_quotations WHERE quotationId = '" . $quotationIdOfCompany . "'";
          $rContainerId = clsDBSingleton::processMultiObjectQuery($sqlContainerId);

          foreach ($rContainerId as $valueContainerId => $keyContainerId) {

            $aContainerIds[$quotationIdOfCompany][] = $keyContainerId->containerId;
            $sqlContainerType = "SELECT type FROM rde_b1mo.containers WHERE containerId = '" . $keyContainerId->containerId . "' LIMIT 1";

            $oContainerType = clsDBSingleton::processSingleObjectQuery($sqlContainerType);
            $containerIdArray[$valueRoutes->routeId][$keyContainerId->containerId] = $oContainerType->type;

            if($oContainerType->type === 'bak') {
              $bakContainerAmount++;
            }
            elseif($oContainerType->type === 'rek') {
              $rekContainerAmount++;
            }
            elseif($oContainerType->type === 'pal') {
              $palContainerAmount++;
            }
            elseif($oContainerType->type === 'opr') {
              $oprContainerAmount++;
            }

          }

          if($oStatusIdQuotation->sendMailToClient === '1') {
            $sendMailToClient = '1';
          }

          if($oStatusIdQuotation->dispatchAppointment != '') {
            $quoteIds .= $oStatusIdQuotation->dispatchAppointment . ' - ';
          }

          if($oStatusIdQuotation->callOrEmailNotes != '') {
            $sCallOrEmailNotes .= $oStatusIdQuotation->callOrEmailNotes . ' - ';
            $sCallOrEmailNotesQuotation = $quotationIdOfCompany;
          }

          $lastQuotationId = $quotationIdOfCompany;
          $lastContainerId = $keyContainerId->containerId;
          $sMeters += $oStatusIdQuotation->meters;

          if($oStatusIdQuotation->statusId < 40) {
            $sMetersUnder40 += $oStatusIdQuotation->meters;
          }

          $bakContainerAmount = 0;
          $rekContainerAmount = 0;
          $palContainerAmount = 0;
          $oprContainerAmount = 0;

          foreach ($containerIdArray as $keyQuote => $valueQuote) {

            foreach ($valueQuote as $keyContainer => $valueContainer) {

              if($valueContainer === 'bak') {
                $bakContainerAmount++;
              }
              elseif($valueContainer === 'rek') {
                $rekContainerAmount++;
              }
              elseif($valueContainer === 'pal') {
                $palContainerAmount++;
              }
              elseif($valueContainer === 'opr') {
                $oprContainerAmount++;
              }

            }

          }

          asort($aStatusIds);
          $aStatusIds = array_values($aStatusIds);
          $lowestStatus = min($aStatusIds);
          $oStatus = $oStatusData->getStatusDataById($lowestStatus);

          //--------------------------------------------------------------------------------------------------
          //--------------------------------------------------------------------------------------------------
          //--------------------------------------------------------------------------------------------------

          //----------------------------------
          if(isset($routeTransport[0]['counter'])) {
            $routeTransport[0]['counter'] = $routeTransport[0]['counter'] + 1;
          }
          else {
            $routeTransport[0]['counter'] = 0;
          }
          $routeTransportKey = $routeTransport[0]['counter'];
          //----------------------------------

          $routeTransport[0]['values'][$routeTransportKey]['addressDeliveryId'] = $oRouteAddressData->addressDeliveryId;
          $routeTransport[0]['values'][$routeTransportKey]['quotationId'] = $quotationIdOfCompany;

          $routeTransport[0]['values'][$routeTransportKey]['orderId'] = $keyRoutes;
          $routeTransport[0]['values'][$routeTransportKey]['longitude'] = $oRouteAddressData->longitude;
          $routeTransport[0]['values'][$routeTransportKey]['latitude'] = $oRouteAddressData->latitude;
          $routeTransport[0]['values'][$routeTransportKey]['street'] = $oRouteAddressData->street;
          $routeTransport[0]['values'][$routeTransportKey]['nr'] = $oRouteAddressData->nr;
          $routeTransport[0]['values'][$routeTransportKey]['extension'] = $oRouteAddressData->extension;

          //-- deze zullen niet werken want $valueRoutes heeft aleen companyId
          $routeTransport[0]['values'][$routeTransportKey]['routeId'] = $valueRoutes->routeId;
          $routeTransport[0]['values'][$routeTransportKey]['subject'] = $valueRoutes->subject;

          //-- deze moet ik nog opzoeken
          // $oCompanyData->name = '';

          // $routeTransport[0]['values'][$routeTransportKey]['subject'] = $oCompanyData->name;
          $routeTransport[0]['values'][$routeTransportKey]['subject'] = '';

          $notes = trim(preg_replace('/\s\s+/', ' ', $valueRoutes->notes));
          $routeTransport[0]['values'][$routeTransportKey]['notes'] = $valueRoutes->notes;
          $routeTransport[0]['values'][$routeTransportKey]['domestic'] = $oRouteAddressData->domestic;
          $routeTransport[0]['values'][$routeTransportKey]['rank'] = $valueRoutes->rank;
          $routeTransport[0]['values'][$routeTransportKey]['truckId'] = $valueRoutes->truckId;

          //----------------------------------

          //-----------------------------------------------------------------------------
          //-----------------------------------------------------------------------------
          //-----------------------------------------------------------------------------

          $oDataQuotationExtra = clsDBSingleton::dbcSelectOne('rde_b1mo.quotations_extra', 'quotationId', $quotationIdOfCompany, '*');
          $oDataAddress = clsDBSingleton::dbcSelectOne('rde_cms.crm_addresses', 'addressId', $oDataQuotationExtra->addressDeliveryId, '*');
          $geoZipcode = str_replace(" ", "", $oDataAddress->zipcode);
          $geoCountry = $oDataAddress->country;
          $geoStreet = $oDataAddress->street . ' ' . $oDataAddress->nr . ' ' . $oDataAddress->extension;
          $geoStreet = urlencode($geoStreet);

          $routeTransport[0]['values'][$routeTransportKey]['lowestStatusId'] = $lowestStatus;
          $routeTransport[0]['values'][$routeTransportKey]['sendMailToClient'] = $sendMailToClient;
          $routeTransport[0]['values'][$routeTransportKey]['quoteIds'] = $quoteIds;
          $routeTransport[0]['values'][$routeTransportKey]['statusImageName'] = $oStatus->imageName;
          $routeTransport[0]['values'][$routeTransportKey]['lastQuotationId'] = $lastQuotationId;
          $routeTransport[0]['values'][$routeTransportKey]['geoZipcode'] = $geoZipcode;
          $routeTransport[0]['values'][$routeTransportKey]['geoCountry'] = $geoCountry;
          $routeTransport[0]['values'][$routeTransportKey]['geoStreet'] = $geoStreet;
          $routeTransport[0]['values'][$routeTransportKey]['sMeters'] = $sMeters;
          $routeTransport[0]['values'][$routeTransportKey]['sMetersUnder40'] = $sMetersUnder40;
          $sqlContainerId = "SELECT containerNumber, type FROM rde_b1mo.containers WHERE containerId = '" . $lastContainerId . "' LIMIT 1";
          $oContainerId = clsDBSingleton::processSingleObjectQuery($sqlContainerId);
          $routeTransport[0]['values'][$routeTransportKey]['lastContainerNumber'] = $oContainerId->containerNumber;
          $routeTransport[0]['values'][$routeTransportKey]['typeContainer'] = $oContainerId->type;
          $routeTransport[0]['values'][$routeTransportKey]['callOrEmailNotes'] = $sCallOrEmailNotes;
          $routeTransport[0]['values'][$routeTransportKey]['callOrEmailNotesQuotation'] = $sCallOrEmailNotesQuotation;
          $routeTransport[0]['values'][$routeTransportKey]['bakAmount'] = $bakContainerAmount;
          $routeTransport[0]['values'][$routeTransportKey]['rekAmount'] = $rekContainerAmount;
          $routeTransport[0]['values'][$routeTransportKey]['palAmount'] = $palContainerAmount;
          $routeTransport[0]['values'][$routeTransportKey]['oprAmount'] = $oprContainerAmount;
          $routeTransport[0]['values'][$routeTransportKey]['mapExtraPoi'] = $valueRoutes->mapExtraPoi;

        }

      }

      //-----------------------------------------------------------------------------
      //-----------------------------------------------------------------------------
      //-----------------------------------------------------------------------------

    }

    $aAddressDeliveryId = [];

    //-- array aanpassen zodat hij goed ingelezen kan worden.
    foreach ($oQuoteNoRouteId as $keyRoutes => $valueRoutes) {

      //-- haal alle quotations op van een klant nog niet zijn ingeplanned.

      $jsonData = '{' . "\n";

      $counterTransport = 0;
      //-- main array routeData
      $jsonData .= '"weekoverview": [{' . "\n";

      $counterTransport = 0;
      foreach ($routeTransport as $keyRouteTransp => $valueRouteTransp) {

        $jsonData .= "\n" . '"' . $keyRouteTransp . '":[' . "\n";

        //-- door transport volvo en iveco heen loopen
        $counterTransport++;
        foreach ($valueRouteTransp as $keyRT => $valueAddressInfo) {

          $counter = 0;
          //-- alle data per order
          foreach ($valueAddressInfo as $keyAI => $valueAI) {

            $jsonData .= '{"addressDeliveryId":"' . $valueAI['addressDeliveryId'] . '",' . "\n";
            $jsonData .= '   "orderId":"' . $valueAI['orderId'] . '",' . "\n";
            $jsonData .= '   "quotationId":"' . $valueAI['quotationId'] . '",' . "\n";
            $jsonData .= '   "longitude":"' . $valueAI['longitude'] . '",' . "\n";
            $jsonData .= '   "latitude":"' . $valueAI['latitude'] . '",' . "\n";
            $jsonData .= '   "street":"' . $valueAI['street'] . '",' . "\n";
            $jsonData .= '   "nr":"' . $valueAI['nr'] . '",' . "\n";
            $jsonData .= '   "extension":"' . $valueAI['extension'] . '",' . "\n";
            $jsonData .= '   "routeId":"' . $valueAI['routeId'] . '",' . "\n";
            $jsonData .= '   "subject":"' . $valueAI['subject'] . '",' . "\n";
            $notes = trim(preg_replace('/\s\s+/', ' ', $valueAI['notes']));
            $jsonData .= '   "notes":"' . $notes . '",' . "\n";
            $jsonData .= '   "domestic":"' . $valueAI['domestic'] . '",' . "\n";
            $jsonData .= '   "rank":"' . $valueAI['rank'] . '",' . "\n";
            $jsonData .= '   "lowestStatusId":"' . $valueAI['lowestStatusId'] . '",' . "\n";
            $jsonData .= '   "emailsentdelivery":"' . $valueAI['emailsentdelivery'] . '",' . "\n";
            $jsonData .= '   "emailsentrackpickup":"' . $valueAI['emailsentrackpickup'] . '",' . "\n";
            $jsonData .= '   "emailsentremovefromroute":"' . $valueAI['emailsentremovefromroute'] . '",' . "\n";
            $jsonData .= '   "sendMailToClient":"' . $valueAI['sendMailToClient'] . '",' . "\n";
            $quoteIds = trim(preg_replace('/\s\s+/', ' ', $valueAI['quoteIds']));
            $jsonData .= '   "quoteIds":"' . $quoteIds . '",' . "\n";
            $callOrEmailNotes = trim(preg_replace('/\s\s+/', ' ', $valueAI['callOrEmailNotes']));
            $jsonData .= '   "callOrEmailNotes":"' . $callOrEmailNotes . '",' . "\n";
            $jsonData .= '   "statusImageName":"' . $valueAI['statusImageName'] . '",' . "\n";
            $jsonData .= '   "lastQuotationId":"' . $valueAI['lastQuotationId'] . '",' . "\n";
            $jsonData .= '   "lastContainerNumber":"' . $valueAI['lastContainerNumber'] . '",' . "\n";
            $jsonData .= '   "callOrEmailNotesQuotation":"' . $valueAI['callOrEmailNotesQuotation'] . '",' . "\n";
            $jsonData .= '   "geoZipcode":"' . $valueAI['geoZipcode'] . '",' . "\n";
            $jsonData .= '   "geoCountry":"' . $valueAI['geoCountry'] . '",' . "\n";
            $jsonData .= '   "geoStreet":"' . $valueAI['geoStreet'] . '",' . "\n";
            $jsonData .= '   "sMeters":"' . $valueAI['sMeters'] . '",' . "\n";
            $jsonData .= '   "sMetersUnder40":"' . $valueAI['sMetersUnder40'] . '",' . "\n";
            $jsonData .= '   "bakAmount":"' . $valueAI['bakAmount'] . '",' . "\n";
            $jsonData .= '   "rekAmount":"' . $valueAI['rekAmount'] . '",' . "\n";
            $jsonData .= '   "palAmount":"' . $valueAI['palAmount'] . '",' . "\n";
            $jsonData .= '   "oprAmount":"' . $valueAI['oprAmount'] . '",' . "\n";
            $jsonData .= '   "mapExtraPoi":"' . $valueAI['mapExtraPoi'] . '",' . "\n";
            $jsonData .= '   "typeContainer":"' . $valueAI['typeContainer'] . '",' . "\n";
            $jsonData .= '   "truckId":"' . $valueAI['truckId'] . '"}';
            $jsonData .= "\n";

            $counter++;
            if(count($valueAddressInfo) === $counter) {
              //-- einde van de loop

              if(count($routeTransport) === $counterTransport) {
                $jsonData .= ']';
              }
              else {
                $jsonData .= '],';
              }

            }
            else {
              $jsonData .= ',';
            }
          }
        }
      }
    }

    $jsonData .= '}],' . "\n";

    $sError .= '      "Error":[';

    $counterQuoteId = 0;
    foreach ($aQuotationsWithoutAddressDeliveryId as $key => $value) {

      if($counterQuoteId === 0) {
        $sError .= '     {';
        $sError .= '         "quotationId":"' . $value . '"';
        $sError .= '     }';
      }
      else {
        $sError .= '     ,{';
        $sError .= '        "quotationId":"' . $value . '"';
        $sError .= '     }';
      }

      $errorString .= '$value: ' . $value;
      $counterQuoteId++;

    }

    $sError .= '  ]';
    $sError .= '} ';

    $jsonData .= $sError;

  }

  echo $jsonData;
