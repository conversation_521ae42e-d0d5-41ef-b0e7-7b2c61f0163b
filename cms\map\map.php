<?php

  require_once($_SERVER['DOCUMENT_ROOT'] . '/config/headercode_global.inc.php');
  $oAuth = new clsAuth();
  $oAuth->validate('/cms/login.php?referer=' . urlencode('map/map.php'));

  //aanroepen vanuit de andere domeinen
  $allowedOrigins = [
    "www.raamdorpel.nl",
    "api.raamdorpel.nl",
    "beheer.raamdorpel.nl",
    "rde-legacy.rde.localhost",
    "example.com.local",
    "gsdprojects.klapjap.nl",
  ];

  if(isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
    $httpString = 'https://';
  }
  else {
    $httpString = 'http://';
  }

  if(in_array($_SERVER["HTTP_HOST"], $allowedOrigins)) {
    header("Access-Control-Allow-Origin: " . $httpString . $_SERVER["HTTP_HOST"]);
  }

  include_once($_SERVER['DOCUMENT_ROOT'] . '/cms/framework/classes/clsMySQL.php');
  include_once($_SERVER['DOCUMENT_ROOT'] . '/cms/map/clsMap.php');
  include_once($_SERVER['DOCUMENT_ROOT'] . '/cms/map/clsMapData.php');

  $oMap = new clsMap();

  clsDBSingleton::instantiate();

  $trucks = clsDBSingleton::processMultiObjectQuery("SELECT * FROM rde_route.gpsbuddy_trucks ORDER BY orderId ASC");


  if($_SERVER['REQUEST_METHOD'] == 'POST') {

    if(isset($_POST['chkSmallOrders']) && $_POST['chkSmallOrders'] == 1) {
      $oMap->smallOrders = true;
    }

    $oMap->showBrands = [];
    if(isset($_POST['chkBrandChinese'])) {
      $oMap->showBrands = array_merge($oMap->showBrands, [4]);
    }
    if(isset($_POST['chkBelgischArduin'])) {
      $oMap->showBrands = array_merge($oMap->showBrands, [6, 7, 8, 9, 10, 11, 12]);
    }

    if(isset($_POST['chkStatus'])) {
      foreach ($_POST['chkStatus'] as $sStatus) {
        $oMap->status = $sStatus;
      }
    }

    //----------------------------------------------------------------------------------------------------------
    //-- als Ontbrekende orders aan staat dan alles laten zien ook in de toekomst
    if(isset($_POST['chkOntOrders']) && $_POST['chkOntOrders'] === '1') {

      $oMap->showMissingOrders = true;

      if(isset($_POST['fldDateRoute4weeks']) && $_POST['fldDateRoute4weeks'] != '') {

        $fourWeeksMinTwoyears = date('Y-m-d', strtotime($_POST['fldDateRoute4weeks'] . ' - 2 years'));
        $oMap->startDateMap = $fourWeeksMinTwoyears;
        $fldDateRoute4weeks = date('Y-m-d', strtotime(date('Y-m-d') . '+ 2 years'));
        $oMap->endDateMap = $fldDateRoute4weeks;

      }

    }
    else {

      $oMap->showMissingOrders = false;

      if(isset($_POST['fldDateRoute4weeks']) && $_POST['fldDateRoute4weeks'] != '') {

        //-- validate date
        $aDateRoute4weeks = explode("-", $_POST['fldDateRoute4weeks']);
        $year = $aDateRoute4weeks[2];
        $month = $aDateRoute4weeks[1];
        $day = $aDateRoute4weeks[0];

        if(checkdate((int)$month, (int)$day, (int)$year) === true) {

          $fourWeeksMinTwoyears = date('Y-m-d', strtotime($_POST['fldDateRoute4weeks'] . ' - 2 years'));
          $oMap->startDateMap = $fourWeeksMinTwoyears;
          $fldDateRoute4weeks = date('Y-m-d', strtotime($_POST['fldDateRoute4weeks']));
          $oMap->endDateMap = $fldDateRoute4weeks;

        }

      }

    }
    //----------------------------------------------------------------------------------------------------------

    //-------------------------------------------------

    if(isset($_POST['chkContainers']) && $_POST['chkContainers'] == 1) {
      $oMap->showContainers = true;
      // echo ' console.log("$oMap->showContainers = true;");' . "\n";
    }

    if(isset($_POST['chkGpsBuddy']) && $_POST['chkGpsBuddy'] == 1) {
      $oMap->showGpsBuddy = true;
    }
    elseif(!isset($_POST['chkGpsBuddy'])) {
      $oMap->showGpsBuddy = false;
    }
    else {
      $oMap->showGpsBuddy = false;
    }

    if(isset($_POST['chkPoi']) && $_POST['chkPoi'] == 1) {
      $oMap->showPoi = true;
    }
    elseif(!isset($_POST['chkPoi'])) {
      $oMap->showPoi = false;
    }
    else {
      $oMap->showPoi = false;
    }

  }
  else {

    //-- let op als hier een nieuwe waarde bij moet dat je hem ook clsMap.php verwerkt
    //-- if(in_array($p_mVarValue, array('20', '30', '40', '50', '53', '55'))) {

    $oMap->status = '20';
    $oMap->status = '30';
    $oMap->status = '35';
    $oMap->status = '38';
    $oMap->status = '40';
    $oMap->status = '50';
    $oMap->status = '53';
    $oMap->status = '55';

    //-- alle datums tonen bij eerste laden zodat je alle errors te zien krijgt.
    $fourWeeksMinTwoyears = date('Y-m-d', strtotime(date('Y-m-d') . ' - 2 years'));
    $oMap->startDateMap = $fourWeeksMinTwoyears;
    $fldDateRoute4weeks = date('Y-m-d', strtotime(date('Y-m-d') . '+ 2 years'));
    $oMap->endDateMap = $fldDateRoute4weeks;

    $oMap->showGpsBuddy = false;
    $oMap->showPoi = false;
    $oMap->showMissingOrders = false;

  }


  $aLocations = $oMap->getMarkerLocationsMap();

  //8020 gemarkeerd als niet gevonden. HIER VERDER ORDER 133193
//  pd($oMap->getNotFound());
  //pd($oMap->getNotFoundLocation());

  $markers = [];
  //pd($aLocations);
  for ($iLoc = 0; $iLoc < count($aLocations); $iLoc++) {
    if(isset($aLocations[$iLoc]['lat'])) {
      if($aLocations[$iLoc]['orderCount'] > 1) {
        if(substr($aLocations[$iLoc]['icon'], -1, 1) == 'd') {
          $sIcon = 'multid';
        }
        else {
          $sIcon = 'multi';
        }
      }
      else {
        $sIcon = $aLocations[$iLoc]['icon'];
      }

      $sZipcode = $aLocations[$iLoc]['geo_zipcode'];
      $sStreet = $aLocations[$iLoc]['geo_street'];
      $sQuotationId = $aLocations[$iLoc]['quotationId'];

      //-- hier worden de punten op de landkaart gegenereerd.
      if($sIcon == '20') {

        $sIcon = 'prepare';
        if($aLocations[$iLoc]['stoneTypeId'] === '2') {
          $sIcon = 'prepareCement';
        }
        elseif($aLocations[$iLoc]['stoneTypeId'] === '3') {
          $sIcon = 'prepareNaturalStone';
        }

        //-- hier moet ik ook een controle inbouwen
        // $sIcon = 'prepare';

      }
      elseif($sIcon == '20d') {
        $sIcon = 'prepareB';
      }
      elseif($sIcon == '30') {

        $sIcon = 'produce';
        if($aLocations[$iLoc]['stoneTypeId'] === '2') {
          $sIcon = 'produceCement';
        }
        elseif($aLocations[$iLoc]['stoneTypeId'] === '3') {
          $sIcon = 'produceNaturalStone';
        }

      }
      elseif($sIcon == '35') {

        $sIcon = 'produceprint';
        if($aLocations[$iLoc]['stoneTypeId'] === '2') {
          $sIcon = 'produceprintCement';
        }
        elseif($aLocations[$iLoc]['stoneTypeId'] === '3') {
          $sIcon = 'produceprintNaturalStone';
        }
      }
      elseif($sIcon == '35d') {
        $sIcon = 'produceprintB';
      }
      elseif($sIcon == '38') {

        $sIcon = 'produceprint';
        if($aLocations[$iLoc]['stoneTypeId'] === '2') {
          $sIcon = 'produceprintCement';
        }
        elseif($aLocations[$iLoc]['stoneTypeId'] === '3') {
          $sIcon = 'produceprintNaturalStone';
        }
      }
      elseif($sIcon == '38d') {
        $sIcon = 'produceprintB';
      }
      elseif($sIcon == '30d') {
        $sIcon = 'produceB';
      }
      elseif($sIcon == '40') {

        $sIcon = 'pending';
        if($aLocations[$iLoc]['stoneTypeId'] === '2') {
          $sIcon = 'pendingCement';
        }
        elseif($aLocations[$iLoc]['stoneTypeId'] === '3') {
          $sIcon = 'pendingNaturalStone';
        }

      }
      elseif($sIcon == '40d') {
        $sIcon = 'pendingB';
      }
      elseif($sIcon == '50') {

        $sIcon = 'deliver';
        if($aLocations[$iLoc]['stoneTypeId'] === '2') {
          $sIcon = 'deliverCement';
        }
        elseif($aLocations[$iLoc]['stoneTypeId'] === '3') {
          $sIcon = 'deliverNaturalStone';
        }

      }
      elseif($sIcon == '50B') {
        $sIcon = 'deliverB';
      }
      elseif($sIcon == '50Bd') {
        $sIcon = 'deliverBd';
      }
      elseif($sIcon == '55') {

        $sIcon = 'delivered';
        if($aLocations[$iLoc]['stoneTypeId'] === '2') {
          $sIcon = 'deliveredCement';
        }
        elseif($aLocations[$iLoc]['stoneTypeId'] === '3') {
          $sIcon = 'deliveredNaturalStone';
        }

      }
      elseif($sIcon == '53') {

        $sIcon = 'loaded';
        if($aLocations[$iLoc]['stoneTypeId'] === '2') {
          $sIcon = 'loadedCement';
        }
        elseif($aLocations[$iLoc]['stoneTypeId'] === '3') {
          $sIcon = 'loadedNaturalStone';
        }

      }
      elseif($sIcon == '53B') {
        $sIcon = 'loadedB';
      }
      elseif($sIcon == '53Bd') {
        $sIcon = 'loadedBd';
      }
      elseif($sIcon == '55B') {
        $sIcon = 'deliveredB';
      }
      elseif($sIcon == '55Bd') {
        $sIcon = 'deliveredBd';
      }
      elseif($sIcon == '60') {
        $sIcon = 'done';
      }
      elseif($sIcon == '61') {
        $sIcon = 'done';
      }
      elseif($sIcon == '62') {
        $sIcon = 'done';
      }
      elseif($sIcon == '63') {
        $sIcon = 'done';
      }
      elseif($sIcon == '70') {
        $sIcon = 'splits';
      }

      $sStreetClean = str_replace("'", "", $sStreet);

      //echo "console.log('".$sIcon."');";

      //        echo "if (typeof customIcons['{$sIcon}'] === 'undefined') ";
      //        echo " { alert('Error: Icon fout gevonden :{$sIcon}: voor :{$sQuotationId}: ";
      //        echo "  {$sZipcode} {$sStreetClean} bestaat nog niet. Daniel laten aanmaken ";
      //        echo "  in cms/map/js/javascript.inc.php'); }" . "\n\n";

      $markers[] = [
        "lat"     => $aLocations[$iLoc]['lat'],
        "lng"     => $aLocations[$iLoc]['lng'],
        "icon"    => $sIcon,
        "country" => $aLocations[$iLoc]['geo_country'],
        "zipcode" => $aLocations[$iLoc]['geo_zipcode'],
        "street"  => $aLocations[$iLoc]['geo_street'],
      ];

    }
  }

  //  pd($markers);


  //-- volvo
  // $currentLicensePlateVolvo = 'Volvo 59-BGS-6';
  // $sQueryTruck3 = "SELECT * FROM rde_route.gpsbuddy_trucks WHERE truckId = 2 LIMIT 1";
  // $oTruck3Info = clsDBSingleton::processSingleObjectQuery($sQueryTruck3);
  // $currentLicensePlateVolvo = $oTruck3Info->name . ' ' .$oTruck3Info->licence;
  $transportVolvoId = 0;

  // $currentLicensePlateIveco = 'Iveco 02-VDR-7';
  // $sQueryTruck2 = "SELECT * FROM rde_route.gpsbuddy_trucks WHERE truckId = 1 LIMIT 1";
  // $oTruck2Info = clsDBSingleton::processSingleObjectQuery($sQueryTruck2);
  // $currentLicensePlateIveco = $oTruck2Info->name . ' ' .$oTruck2Info->licence;
  $transportIvecoId = 1;

  $aLastData = [];

  if($oMap->showGpsBuddy === true) {

    $aLastData = file_get_contents("https://beheer.raamdorpel.nl/nl/external?action=trucklocations");
    $aLastData = json_decode($aLastData);

    if($aLastData->result === false) {
      unset($aLastData);
      $aLastData[$transportVolvoId] = new stdClass();
      $aLastData[$transportIvecoId] = new stdClass();
      $aLastData[$transportVolvoId]->latitude = '';
      $aLastData[$transportIvecoId]->latitude = '';
    }

  }
  else {

    /*
    echo '<p>Kom ik hier? '.__LINE__.' '.__FILE__.'</p>';
    echo '<pre>';
    var_dump($transportVolvoId);
    echo '</pre>';
    */

    $aLastData[$transportVolvoId] = new stdClass();
    $aLastData[$transportIvecoId] = new stdClass();


    $aLastData[$transportVolvoId]->latitude = '';
    $aLastData[$transportIvecoId]->latitude = '';

  }

?>
<!DOCTYPE html>
<html>
<head>
  <title>Landkaart</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="initial-scale=1.0, user-scalable=no"/>

  <!-- deze is nodig voor de modal/popup functie -->
  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css"/>

  <script type="text/javascript" src="/js/ajax.js"></script>
  <script src="https://code.jquery.com/jquery-3.1.1.min.js"
          integrity="sha256-hVVnYaiADRTO2PzUGmuLJr8BLUSjGIZsDYGmIJLv2b8="
          crossorigin="anonymous"></script>

  <link type="text/css" rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css"/>
  <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
  <link rel="stylesheet" type="text/css" href="style.css?v=<?php echo VERSION ?>">

  <!-- straks weer aanzetten maar voor snelheid evenuit zetten. -->
  <?php require_once 'js/javascript.inc.php'; ?>
  <script src="js/getRouteData.js"></script>
  <?php require_once 'js/changeDateRoute.inc.php'; ?>
  <?php require_once 'js/updateRouteDisplay.php'; ?>
  <?php require_once 'js/updateUnplannedOverview.php'; ?>
  <?php require_once 'js/updateLinkedUnplannedOverview.php'; ?>

  <!-- deze is nodig voor de modal/popup functie -->
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js"></script>
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.bundle.min.js"></script>

  <script src="js/ajaxSendEmailByQuote.js"></script>
  <script src="js/selectAllPopupSendEmail.js"></script>
  <script src="js/getRouteData.js"></script>
  <script src="js/ajaxUpdateDateRoute.js"></script>

  <script>

    function GPSBuddyLogin() {
      document.forms['hiddenLoginForm'].submit();
    }

    $(document).ready(function () {

      $(document).on("click", ".btnSendEmailToQuotationList", function (event) {
        //-- loop door de quotationId's heen
        $.each($("input[name='chkQuotations']:checked"), function () {
          sendEmailByQuote($(this).val());
        });
      });
    });

  </script>

</head>
<body onload="initialize()">
<div id="dhtmltooltip"></div>
<script src="/cms/crm/finance/js/dhtml-tooltip.js"></script>
<div id="mapcontainer">
  <div id="left">
    <?php
      require_once 'map-left.inc.php';
    ?>
  </div>

  <input type="text" id="mapsearch" size="50">

  <!-- deze later aanzetten -->
  <div id="map_canvas"></div>

  <div id="right_canvas">
    <?php require_once 'map-right.inc.php'; ?>
  </div>
</div>
<script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCNzp5UcCwH7ftxdS799-JYuc1nXHGunHk&language=nl&language=nl&libraries=places"></script>

</body>
</html>
