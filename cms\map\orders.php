<?php

  require_once($_SERVER['DOCUMENT_ROOT'] . '/config/headercode_global.inc.php');
  $oAuth = new clsAuth();
  $oAuth->validate('/cms/login.php?referer=' . urlencode('map/map.php'));

  include_once('../framework/classes/clsMySQL.php');
  include_once('clsMap.php');
  include_once('clsMapData.php');

  $oMap = new clsMap();
  $oMapData = new clsMapData();
  $oXML = new DOMDocument('1.0');
  $oXML->formatOutput = true;

  $oResult = $oXML->createElement('result');
  $oXML->appendChild($oResult);

  if(isset($_GET['country']) && isset($_GET['zipcode'])) {

    # Set parameters
    $oMap->country = $_GET['country'];
    $oMap->zipcode = $_GET['zipcode'];

    //-- street value is not set if countyr = NL
    if(isset($_GET['street'])) {
      $oMap->street = $_GET['zipcode'];
    }

    if(isset($_GET['orders']) && $_GET['orders'] == 'N') {
      $sShowOrders = false;
    }
    else {
      $sShowOrders = true;
    }

    if(isset($_GET['containers'])) {
      $oMap->showContainers = ($_GET['containers'] == 'Y') ? true : false;
    }
    else {
      $oMap->showContainers = false;
    }

    # Get the orders
    if($sShowOrders) {

      foreach ($oMap->getOrdersMap() as $aOrder) {

        $oOrder = $oXML->createElement('order');

        foreach ($aOrder as $sTag => $sContent) {
          if(!is_numeric($sTag)) {

            if(in_array($sTag, ['companyName', 'deliverDomestic', 'orderNotes', 'zipcode', 'plannedDeliveryDate'])) {
              # CDATA section

              if($sTag == 'orderNotes') {
                $sContent = nl2br($sContent);
              }

              $oCElement = $oXML->createElement($sTag);
              $oCElement->appendChild($oXML->createCDATASection($sContent));
              $oOrder->appendChild($oCElement);

            }
            else {
              # Normal

              $sContent = ($sTag == 'orderNumber') ? strtoupper($sContent) : $sContent;

              $oElement = $oXML->createElement($sTag, $sContent);
              $oOrder->appendChild($oElement);

            }
          }
        }

        $oResult->appendChild($oOrder);

      }
    }

    if($oMap->showContainers) {

      //-- filter containerId to remove duplicates.
      //-- het maakt namelijk niet uit welke hij selecteert.
      $aNewContainerIdList = [];
      $aContainerComplete = [];

      foreach ($oMap->getContainers() as $aContainerValues) {

        /*
         * //-- als ik weer duplicated op containerId niveau wil verwijderen
         * //-- dan moet ik deze aan zetten.
         *
        if (!in_array($aContainerValues['containerId'], $aNewContainerIdList)) {
            $aNewContainerIdList[] = $aContainerValues['containerId'];
            $aContainerComplete[] = $aContainerValues;
        }
        */

        //-- filter hier uitzetten zodat ik alle resultaten kan zien


        //-- hier controleer ik op duplicates binnen de multi array
        $valueExists = false;
        foreach ($aNewContainerIdList as $keyCIL => $valueCIL) {
          if($aContainerValues['containerId'] === $valueCIL) {
            $valueExists = true;
          }
        }

        if($valueExists === false) {
          $aNewContainerIdList[] = $aContainerValues['containerId'];
          $aContainerComplete[] = $aContainerValues;
        }

      }

      foreach ($aContainerComplete as $aContainer) {

        //-- filter containerId to remove duplicates.
        $aContainerIdList = [];
        $aNewContainerIdList = [];
        foreach ($aContainerIdList as $key => $value) {

          //-- niet meer filteren
          // $aColorList[] = $value->colorId;
          // $aNewContainerIdList[] = $value;

          if(!in_array($value->colorId, $aContainerIdList)) {
            $aColorList[] = $value->colorId;
            $aNewContainerIdList[] = $value;
          }
        }

        //-- hier een filter inbouwen voor dubbele waardes

        /* tijdelijk uitschakkelen.
           // bart wil graag dat de landkaart niet knippert maar links van de landkaart wel
           // vandaar dat deze controle uit staat.
            if ($aContainer['icon'] === 'rekd') {
                $oContainerDoNotReturn = $oMapData->getCompanyContainerDoNotReturn($aContainer['realCompanyId']);
                if ($oContainerDoNotReturn->containerDoNotReturn === '1') {
                    $aContainer['icon'] = 'rek';
                }
            }

            if ($aContainer['icon'] === 'bakd') {
                $oContainerDoNotReturn = $oMapData->getCompanyContainerDoNotReturn($aContainer['realCompanyId']);
                if ($oContainerDoNotReturn->containerDoNotReturn === '1') {
                    $aContainer['icon'] = 'bak';
                }
            }
        */

        $oContainers = $oXML->createElement('container');

        foreach ($aContainer as $sTag => $sContent) {

          if(!is_numeric($sTag)) {
            if(in_array($sTag, ['companyName', 'deliverDomestic', 'orderNotes'])) {
              # CDATA
              if($sTag == 'orderNotes') {
                $sContent = nl2br($sContent);
              }

              $oCElement = $oXML->createElement($sTag);
              $oCElement->appendChild($oXML->createCDATASection($sContent));
              $oContainers->appendChild($oCElement);

            }
            else {
              # Normal
              $sContent = ($sTag == 'orderNumber') ? strtoupper($sContent) : $sContent;

              $oElement = $oXML->createElement($sTag, $sContent);
              $oContainers->appendChild($oElement);

            }
          }
        }

        $oResult->appendChild($oContainers);

      }
    }
  }

  header('Content-type: text/xml');

  echo $oXML->saveXML();

  unset($oMap, $oXML);

