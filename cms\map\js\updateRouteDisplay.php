<script type="text/javascript">

    function dissconnectContainer(routeId) {

        $.ajax({
            type: 'GET',
            dataType: "json",
            data: {routeId: routeId},
            url: "ajax/ajaxDisconnectAndDeleteContainer.php",
            success: function(data) {
                console.log('update gelukt cgwHynnK: ' + routeId);
            },
            error: function(jqXHR, exception) {
                console.log('error code uWixDbIr');
                if (jqXHR.status === 0) {
                    console.log('Not connect.n Verify Network.');
                    alert('Not connect.n Verify Network.');
                } else if (jqXHR.status == 404) {
                    console.log('Requested page not found. [404]');
                    alert('Requested page not found. [404]');
                } else if (jqXHR.status == 500) {
                    console.log('Internal Server Error [500].');
                    alert('Internal Server Error [500].');
                } else if (exception === 'parsererror') {
                    console.log('Requested JSON parse failed. possible that its not well formatted. code 3f34g3h34');
                    alert('Requested JSON parse failed. possible that its not well formatted. code 3f34g3h34');
                } else if (exception === 'timeout') {
                    console.log('Time out error.');
                    alert('Time out error.');
                } else if (exception === 'abort') {
                    console.log('Ajax request aborted.');
                    alert('Ajax request aborted.');
                } else {
                    console.log('Uncaught Error.n' + jqXHR.responseText);
                    alert('Uncaught Error.n' + jqXHR.responseText);
                }
            }
        }); // $.ajax({


    }

    function dissconnectPoi(routeId) {

        $.ajax({
            type: 'GET',
            dataType: "json",
            data: {routeId: routeId},
            url: "ajax/ajaxDisconnectAndDeletePoi.php",
            success: function(data) {
                // console.log('update gelukt bOlWGLph');
            },
            error: function(jqXHR, exception) {
                console.log('error code bOlWGLph');
                if (jqXHR.status === 0) {
                    console.log('Not connect.n Verify Network.');
                    alert('Not connect.n Verify Network.');
                } else if (jqXHR.status == 404) {
                    console.log('Requested page not found. [404]');
                    alert('Requested page not found. [404]');
                } else if (jqXHR.status == 500) {
                    console.log('Internal Server Error [500].');
                    alert('Internal Server Error [500].');
                } else if (exception === 'parsererror') {
                    console.log('Requested JSON parse failed. possible that its not well formatted. code efwefwe23423');
                    alert('Requested JSON parse failed. possible that its not well formatted. code efwefwe23423');
                } else if (exception === 'timeout') {
                    console.log('Time out error.');
                    alert('Time out error.');
                } else if (exception === 'abort') {
                    console.log('Ajax request aborted.');
                    alert('Ajax request aborted.');
                } else {
                    console.log('Uncaught Error.n' + jqXHR.responseText);
                    alert('Uncaught Error.n' + jqXHR.responseText);
                }
            }
        }); // $.ajax({


    }

    function dissconnectRoute(routeId) {

        $.ajax({
            type: 'GET',
            dataType: "json",
            data: {routeId: routeId},
            url: "ajax/ajaxDisconnectAndDeleteRoute.php",
            success: function(data) {
                // console.log('update gelukt seCBuPbH');
            },
            error: function(jqXHR, exception) {
                console.log('error code uWixDbIr');
                if (jqXHR.status === 0) {
                    console.log('Not connect.n Verify Network.');
                    alert('Not connect.n Verify Network.');
                } else if (jqXHR.status == 404) {
                    console.log('Requested page not found. [404]');
                    alert('Requested page not found. [404]');
                } else if (jqXHR.status == 500) {
                    console.log('Internal Server Error [500].');
                    alert('Internal Server Error [500].');
                } else if (exception === 'parsererror') {
                    console.log('Requested JSON parse failed. possible that its not well formatted. code 76k54h5g5');
                    alert('Requested JSON parse failed. possible that its not well formatted. code 76k54h5g5');
                } else if (exception === 'timeout') {
                    console.log('Time out error.');
                    alert('Time out error.');
                } else if (exception === 'abort') {
                    console.log('Ajax request aborted.');
                    alert('Ajax request aborted.');
                } else {
                    console.log('Uncaught Error.n' + jqXHR.responseText);
                    alert('Uncaught Error.n' + jqXHR.responseText);
                }
            }
        }); // $.ajax({


    }

    function updateRank(dataRankIds) {

        $.ajax({
            type: 'GET',
            dataType: "json",
            data: {dataRankIds: dataRankIds},
            url: "ajax/ajaxUpdateRank.php",
            success: function(data) {
                console.log('update gelukt g6MVe0ferfierhferuih');
            },
            error: function(jqXHR, exception) {
                console.log('error code LXzkp9J9');
                if (jqXHR.status === 0) {
                    console.log('Not connect.n Verify Network.');
                    alert('Not connect.n Verify Network.');
                } else if (jqXHR.status == 404) {
                    console.log('Requested page not found. [404]');
                    alert('Requested page not found. [404]');
                } else if (jqXHR.status == 500) {
                    console.log('Internal Server Error [500].');
                    alert('Internal Server Error [500].');
                } else if (exception === 'parsererror') {
                    console.log('Requested JSON parse failed. possible that its not well formatted. code 4t349t348h8g4');
                    alert('Requested JSON parse failed. possible that its not well formatted. code 4t349t348h8g4');
                } else if (exception === 'timeout') {
                    console.log('Time out error.');
                    alert('Time out error.');
                } else if (exception === 'abort') {
                    console.log('Ajax request aborted.');
                    alert('Ajax request aborted.');
                } else {
                    console.log('Uncaught Error.n' + jqXHR.responseText);
                    alert('Uncaught Error.n' + jqXHR.responseText);
                }
            }
        }); // $.ajax({


    }

    /**
     *
     * @param country
     * @param zipcode
     * @param nr
     * @param domestic
     * @param extrainfo
     * @param companyname
     * @param street
     */
    function showPoiLinks(country, zipcode, nr, domestic, extrainfo, companyname, street) {

        $.ajax({
            type: 'GET',
            dataType: "json",
            data: {country: country, zipcode: zipcode, nr: nr, domestic: domestic, companyname: companyname, street: street},
            url: "ajax/ajaxGetAddressIds.php",
            success: function(data) {

                sHTML = '<h1>Point of interest</h1>';
                if (extrainfo != '') {
                    sHTML += '<p><b>Extrainfo: ' + extrainfo + '</b></p>';
                }
                sHTML += '<p><b>' + companyname + '</b></p>';
                sHTML += '<p><b>' + street + ' ' + nr + ' ' + zipcode + ' ' + domestic + '</b></p>';

                for (var key in data.addressIds) {
                    sHTML += '<p><a href="/cms/crm/crm.php?tabroute=routepoi&addressid='+data.addressIds[key].addressDeliveryId+'" target="_blank">Voeg POI to aan route</a></p>';
                }

                document.getElementById('locationOrders').innerHTML = sHTML;

            },
            error: function(jqXHR, exception) {
                console.log('error code POQwDIEv');
                if (jqXHR.status === 0) {
                    console.log('Not connect.n Verify Network.');
                    alert('Not connect.n Verify Network.');
                } else if (jqXHR.status == 404) {
                    console.log('Requested page not found. [404]');
                    alert('Requested page not found. [404]');
                } else if (jqXHR.status == 500) {
                    console.log('Internal Server Error [500].');
                    alert('Internal Server Error [500].');
                } else if (exception === 'parsererror') {
                    console.log('Requested JSON parse failed. possible that its not well formatted. code s2s2s2s');
                    alert('Requested JSON parse failed. possible that its not well formatted. code s2s2s2s');
                } else if (exception === 'timeout') {
                    console.log('Time out error.');
                    alert('Time out error.');
                } else if (exception === 'abort') {
                    console.log('Ajax request aborted.');
                    alert('Ajax request aborted.');
                } else {
                    console.log('Uncaught Error.n' + jqXHR.responseText);
                    alert('Uncaught Error.n' + jqXHR.responseText);
                }
            }
        }); // $.ajax({

    }


    //-- kort uitleg van deze functie

    //-- 1. maakt rechter tabel leeg
    //-- 2. vult rechter tabel
    //-- 3. maakt een sorteerbaar overzicht per vrachtwagen
    //-- 4. controle of er een vakje verwijderd mag verwijdered worden
    //      afhankelijk van de mails die zijn verzonden
    //-- 5. alleen de tranport rijden laten zien die een route bevatten.


    /**
     *
     * @param dateValue
     * @param callback
     */
    function updateRouteOverview(dateValue, callback) {

        var aChangeDate = dateValue.split("-");
        //-- huidige datum instellen
        if (aChangeDate[2].length === 4) {
            //-- then switch
            var dateValue = aChangeDate[2] +'-'+ aChangeDate[1] +'-'+ aChangeDate[0];
        }

        $.ajax({
            type: 'GET',
            dataType: "json",
            data: {date: dateValue},
            url: "ajax/ajaxGetRouteData.php",
            success: function(data) {

                console.log('dateValue: ' + dateValue);

                var splitDateValue = dateValue.split('-');
                if (splitDateValue[0].length !== 4) {
                    console.log('dateValue 487f43f78: ' + dateValue);
                    console.log('GEEN GELDIGE DATUM bij updateRouteOverview. ' +
                        'Soms is dit nodig om te kijken of er geen routes meer op deze datum staan.');
                } else {
                    // console.log('waarschijlijk een geldige ');
                }

                arrayOfTruckIdAndName = [];

                <?php

                    clsDBSingleton::instantiate();
                    $sQuery = "SELECT * FROM rde_route.gpsbuddy_trucks ORDER BY orderId ASC";
                    $oRoutes = clsDBSingleton::processMultiObjectQuery($sQuery);
                    foreach ($oRoutes AS $keyRoute => $valueRoute) {

                        echo "$('<h1>').attr({    }).appendTo('#waypointFormTruckId".$valueRoute->truckId."');";

                        //-- alles legen
                        echo '$("#sortable'.$valueRoute->truckId.'").html(\'\');' . "\n";
                        echo '$("#waypointInside'.$valueRoute->truckId.'").html(\'\');' . "\n";
                        echo '$("#printStatusLink'.$valueRoute->truckId.'").html(\'\');' . "\n";
                        echo '$("#totaalAantalMeters'.$valueRoute->truckId.'").html(\'\');' . "\n";
                        echo '$("#directions-panel'.$valueRoute->truckId.'").html(\'\');' . "\n";
                        echo '$("#directions-panel'.$valueRoute->truckId.'").css("display", "none");';

                        //-- voeg de begin waarde toe
                        echo '$("#sortable'.$valueRoute->truckId.'").append(\'<li class="ui-state-default ui-state-disabled" id="aid_00000">Raambrug 9, Bladel</li>\');' . "\n";

                        //-- deze is nodig om niet de route te tonen die niet aanwezig is.
                        echo 'arrayOfTruckIdAndName.push({"truckid": "'.$valueRoute->truckId.'", "truckname": "'.$valueRoute->name.'"});' . "\n";

                    }

                ?>

                //-----------------------------------------------------------------
                //-- toon de waardes van data
                //--

                //-- 2. vult rechter tabel

                var alphabet = ['A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z'];
                alphabet.push('AA','AB','AC','AD','AE','AF','AG','AH','AI','AJ','AK','AL','AM','AN','AO','AP','AQ','AR','AS','AT','AU','AV','AW','AX','AY','AZ');

                var allQuotationsVolvo = [];
                var allQuotationsVolvoSubject = [];
                var allQuotationsVolvoDomestic = [];
                var allQuotationsVolvoEmailSentDelivery = [];
                var allQuotationsVolvoEmailRackPickup = [];
                var allQuotationsVolvoEmailRemoveFromRoute = [];
                var allQuotationsVolvoRouteId = [];
                var allQuotationsVolvoContainer = [];

                var allQuotationsAfhalen = [];
                var allQuotationsAfhalenSubject = [];
                var allQuotationsAfhalenDomestic = [];
                var allQuotationsAfhalenEmailSentDelivery = [];
                var allQuotationsAfhalenEmailRackPickup = [];
                var allQuotationsAfhalenEmailRemoveFromRoute = [];
                var allQuotationsAfhalenRouteId = [];
                var allQuotationsAfhalenContainer = [];

                var allQuotationsIveco = [];
                var allQuotationsIvecoSubject = [];
                var allQuotationsIvecoDomestic = [];
                var allQuotationsIvecoEmailSentDelivery = [];
                var allQuotationsIvecoEmailRackPickup = [];
                var allQuotationsIvecoEmailRemoveFromRoute = [];
                var allQuotationsIvecoRouteId = [];
                var allQuotationsIvecoContainer = [];

                var allQuotationsCrafter = [];
                var allQuotationsCrafterSubject = [];
                var allQuotationsCrafterDomestic = [];
                var allQuotationsCrafterEmailSentDelivery = [];
                var allQuotationsCrafterEmailRackPickup = [];
                var allQuotationsCrafterEmailRemoveFromRoute = [];
                var allQuotationsCrafterRouteId = [];
                var allQuotationsCrafterContainer = [];

                var objQuotationType = [];

                var stringObjectArray = '';

                if (data.truckid.Volvo !== undefined) {
                    //-- volvo tonen
                    $("#sortableContainer2").css("display", "block");
                } else {
                    //-- volvo uitschakkelen want deze is niet zichtbaar

                    $("#sortableContainer2").css("display", "none");
                }

                if (data.truckid.Iveco !== undefined) {
                    $("#sortableContainer1").css("display", "block");
                } else {
                    //-- iveco uitschakkelen
                    $("#sortableContainer1").css("display", "none");
                }

                // sortableContainer3
                //-- afhalen staat altijd uit,
                // $("#sortableContainer3").css("display", "none");

                if (data.truckid.Afhalen !== undefined) {
                    $("#sortableContainer3").css("display", "block");
                } else {
                    //-- crafter uitschakkelen
                    $("#sortableContainer3").css("display", "none");
                }



                if (data.truckid.Crafter !== undefined) {
                    $("#sortableContainer4").css("display", "block");
                } else {
                    //-- crafter uitschakkelen
                    $("#sortableContainer4").css("display", "none");
                }

                // console.log('show all truckIds');
                // console.dir(data.truckid);

                //-- verwijder de sortable waardes
                for (var key in data.truckid) {

                    var sMetersTotaal = 0;
                    var sMetersUnder40Totaal = 0;
                    var sMetersString = '';

                    for (var key2 in data.truckid[key]) {

                        // console.log('data.truckid[key].length: ' + data.truckid[key].length + ' key: ' + key);

                        if (key === 'Volvo') {
                            allQuotationsVolvo.push(data.truckid[key][key2]['lastQuotationId']);
                            allQuotationsVolvoSubject.push(data.truckid[key][key2]['subject']);
                            allQuotationsVolvoDomestic.push(data.truckid[key][key2]['domestic']);
                            allQuotationsVolvoEmailSentDelivery.push(data.truckid[key][key2]['emailsentdelivery']);
                            allQuotationsVolvoEmailRackPickup.push(data.truckid[key][key2]['emailsentrackpickup']);
                            allQuotationsVolvoEmailRemoveFromRoute.push(data.truckid[key][key2]['emailsentremovefromroute']);
                            allQuotationsVolvoRouteId.push(data.truckid[key][key2]['routeId']);
                        }

                        if (key === 'Afhalen') {
                            allQuotationsAfhalen.push(data.truckid[key][key2]['lastQuotationId']);
                            allQuotationsAfhalenSubject.push(data.truckid[key][key2]['subject']);
                            allQuotationsAfhalenDomestic.push(data.truckid[key][key2]['domestic']);
                            allQuotationsAfhalenEmailSentDelivery.push(data.truckid[key][key2]['emailsentdelivery']);
                            allQuotationsAfhalenEmailRackPickup.push(data.truckid[key][key2]['emailsentrackpickup']);
                            allQuotationsAfhalenEmailRemoveFromRoute.push(data.truckid[key][key2]['emailsentremovefromroute']);
                            allQuotationsAfhalenRouteId.push(data.truckid[key][key2]['routeId']);
                        }

                        if (key === 'Iveco') {
                            allQuotationsIveco.push(data.truckid[key][key2]['lastQuotationId']);
                            allQuotationsIvecoSubject.push(data.truckid[key][key2]['subject']);
                            allQuotationsIvecoDomestic.push(data.truckid[key][key2]['subject']);
                            allQuotationsIvecoEmailSentDelivery.push(data.truckid[key][key2]['emailsentdelivery']);
                            allQuotationsIvecoEmailRackPickup.push(data.truckid[key][key2]['emailsentrackpickup']);
                            allQuotationsIvecoEmailRemoveFromRoute.push(data.truckid[key][key2]['emailsentremovefromroute']);
                            allQuotationsIvecoRouteId.push(data.truckid[key][key2]['routeId']);
                        }

                        if (key === 'Crafter') {
                            allQuotationsCrafter.push(data.truckid[key][key2]['lastQuotationId']);
                            allQuotationsCrafterSubject.push(data.truckid[key][key2]['subject']);
                            allQuotationsCrafterDomestic.push(data.truckid[key][key2]['subject']);
                            allQuotationsCrafterEmailSentDelivery.push(data.truckid[key][key2]['emailsentdelivery']);
                            allQuotationsCrafterEmailRackPickup.push(data.truckid[key][key2]['emailsentrackpickup']);
                            allQuotationsCrafterEmailRemoveFromRoute.push(data.truckid[key][key2]['emailsentremovefromroute']);
                            allQuotationsCrafterRouteId.push(data.truckid[key][key2]['routeId']);
                        }

                        var geenLng = '';
                        if (data.truckid[key][key2]['longitude'] === '0.000000000000000') { geenLng = 'GEEN LNG!!!!' }
                        var fullAddress = data.truckid[key][key2]['street'] + ' ' + data.truckid[key][key2]['nr'];
                        if (data.truckid[key][key2]['extension'] !== '') {
                            fullAddress = fullAddress + ' ' + data.truckid[key][key2]['extension'] + ', ';
                        } else {
                            fullAddress = fullAddress + ', ';
                        }

                        $('<input>').attr({
                            type: 'hidden',
                            class: 'wayptsclass' + data.truckid[key][key2]['truckId'],
                            name: 'bar',
                            disabled: 'disabled',
                            style: 'width:250px;',
                            value: data.truckid[key][key2]['latitude'] + ',' + data.truckid[key][key2]['longitude']
                        }).appendTo('#waypointInside' + data.truckid[key][key2]['truckId']);

                        $('<input>').attr({
                            type: 'hidden',
                            class: 'wayptsclassGeoInfo' + data.truckid[key][key2]['truckId'],
                            name: 'bar',
                            disabled: 'disabled',
                            style: 'width:250px;',
                            value: data.truckid[key][key2]['geoZipcode'] + ',' + data.truckid[key][key2]['geoCountry'] + ',' +data.truckid[key][key2]['geoStreet']
                        }).appendTo('#waypointInside' + data.truckid[key][key2]['truckId']);

                        var keyPlusOne = parseInt(key2) + 1;

                        var echoAllRed = false;
                        if (data.truckid[key][key2]['lowestStatusId'] == 40) {
                            //-- alle tekst moet rood worden
                            echoAllRed = true;
                        }

                        var stringStatusImage = '';
                        if (data.truckid[key][key2]['lowestStatusId'] < 60) {
                            if (data.truckid[key][key2]['statusImageName'] != '') {
                                stringStatusImage = '<img src="/cms/images/icons/' + data.truckid[key][key2]['statusImageName'] + '" title="' + data.truckid[key][key2]['lowestStatusId'] + '" />';
                            }
                        }

                        if (data.truckid[key][key2]['mapExtraPoi'] == 1) {

                            //-- POI
                            var stringIdName =  'aidpoi_';

                            var stringLinkToRoute = '';
                            var stringLinkToRouteInner = '';

                            if (data.truckid[key][key2]['rekAmount'] > 0) {
                                stringLinkToRouteInner += '<div class="typeAmountInner">'+
                                '<a href="#" style="text-decoration: underline;"'+
                                ' onclick="window.open(\'/cms/crm/crm.php?tabroute=routecontainer&containerid=' +
                                data.truckid[key][key2]['lastContainerNumber']+'\',\'_blank\');">' +
                                '<b>R:</b>'+data.truckid[key][key2]['rekAmount']+'</a></div>';
                                console.log('R: is true');
                            }

                            if (data.truckid[key][key2]['bakAmount'] > 0) {
                                stringLinkToRouteInner += '<div class="typeAmountInner">' +
                                '<a href="#" style="text-decoration: underline;" ' +
                                'onclick="window.open(\'/cms/crm/crm.php?tabroute=routecontainer&containerid=' +
                                data.truckid[key][key2]['lastContainerNumber']+'\',\'_blank\');">' +
                                '<b>B:</b>'+data.truckid[key][key2]['bakAmount']+'</a></div>';
                            }

                            stringLinkToRoute += '<div class="containerTypeAmount">';
                            stringLinkToRoute += stringLinkToRouteInner;
                            stringLinkToRoute += '</div>';

                            if (data.truckid[key][key2]['emailsentrackpickup'] == 1) {
                                stringLinkToRoute += '<div class="containerTypeAmount" id="emailColorIndicator_' +
                                    data.truckid[key][key2]['lastQuotationId'] +
                                    '_' + data.truckid[key][key2]['lastQuotationId']+'">';
                                stringLinkToRoute += '<img class="imageEmailSent" src="/cms/images/icons/email-sent-green.png" alt="pickup email sent" title="pickup email sent" />';
                                stringLinkToRoute += '</div>';
                            }

                        } else {

                            //-- dit is een container die opgehaald moet worden
                            //-- bakken en rekken mogen niet verwijderd worden.
                            var stringIdName = 'aidc_';
                            var stringLinkToRoute = '';
                            var stringLinkToRouteInner = '';

                            if (data.truckid[key][key2]['rekAmount'] > 0) {
                                stringLinkToRouteInner += '<div class="typeAmountInner">'+
                                '<a href="#" style="text-decoration: underline;"' +
                                ' onclick="window.open(\'/cms/crm/crm.php?tabroute=routecontainer&containerid=' +
                                data.truckid[key][key2]['lastContainerNumber']+'\',\'_blank\');">' +
                                '<b>R:</b>'+
                                data.truckid[key][key2]['rekAmount']+
                                '</a></div>';
                            }

                            if (data.truckid[key][key2]['bakAmount'] > 0) {
                                stringLinkToRouteInner += '<div class="typeAmountInner">' +
                                '<a href="#" style="text-decoration: underline;" ' +
                                'onclick="window.open(\'/cms/crm/crm.php?tabroute=routecontainer&containerid=' +
                                data.truckid[key][key2]['lastContainerNumber']+'\',\'_blank\');">' +
                                '<b>B:</b>'+
                                data.truckid[key][key2]['bakAmount']+
                                '</a></div>';
                            }

                            stringLinkToRoute += '<div class="containerTypeAmount">';
                            stringLinkToRoute += stringLinkToRouteInner;
                            stringLinkToRoute += '</div>';

                            stringLinkToRoute += '<div id="imageEmailSent_' +
                                data.truckid[key][key2]['lastQuotationId'] +
                                '_' + data.truckid[key][key2]['routeId'] + '" >';

                            if (data.truckid[key][key2]['emailsentremovefromroute'] == 1) {
                                stringLinkToRoute += '<img id="emailRemove_' +
                                    data.truckid[key][key2]['routeId'] +
                                    '" class="imageEmailSent" src="/cms/images/icons/email-sent-red.png" ' +
                                    'title="remove from route email sent" alt="remove from route email sent" />';
                            }

                            if (data.truckid[key][key2]['emailsentrackpickup'] == 1) {
                                stringLinkToRoute += '<img id="emailPickup_' +
                                    data.truckid[key][key2]['routeId'] +
                                    '" class="imageEmailSent" src="/cms/images/icons/email-sent-green.png" ' +
                                    'alt="pickup email sent" title="pickup email sent" />';
                            }

                            stringLinkToRoute += '</div>';

                            if (data.truckid[key][key2]['lowestStatusId'] < 60) {

                                //-- dit is een leveradres die geleverd moet worden.
                                stringIdName =  'aid_';

                                stringLinkToRoute = '';
                                stringLinkToRoute += '<div class="containerTypeAmount">';
                                var isContainerTrue = false;
                                if (data.truckid[key][key2]['rekAmount'] > 0) {

                                    stringLinkToRoute += '<div class="typeAmountInner">' +
                                    '<a href="#" style="text-decoration: underline;"' +
                                    ' onclick="window.open(\'/cms/crm/crm.php?tabroute=route&quotationid=' +
                                    data.truckid[key][key2]['lastQuotationId']+'\',\'_blank\');">' +
                                    '<b>R:</b>'+
                                    data.truckid[key][key2]['rekAmount']+'</a></div>';
                                    isContainerTrue = true;

                                }
                                if (data.truckid[key][key2]['bakAmount'] > 0) {

                                    stringLinkToRoute += '<div class="typeAmountInner">'+
                                    '<a href="#" style="text-decoration: underline;"'+
                                    ' onclick="window.open(\'/cms/crm/crm.php?tabroute=route&quotationid=' +
                                    data.truckid[key][key2]['lastQuotationId']+'\',\'_blank\');">'+
                                    '<b>B:</b>'+
                                    data.truckid[key][key2]['bakAmount']+'</a></div>';
                                    isContainerTrue = true;

                                }
                                stringLinkToRoute += '</div>';

                                if (isContainerTrue === true) {
                                    if (key === 'Volvo') { allQuotationsVolvoContainer.push('1'); }
                                    if (key === 'Afhalen') { allQuotationsAfhalenContainer.push('1'); }
                                    if (key === 'Iveco') { allQuotationsIvecoContainer.push('1'); }
                                    if (key === 'Crafter') { allQuotationsCrafterContainer.push('1'); }
                                } else {
                                    if (key === 'Volvo') { allQuotationsVolvoContainer.push('0'); }
                                    if (key === 'Afhalen') { allQuotationsAfhalenContainer.push('0'); }
                                    if (key === 'Iveco') { allQuotationsIvecoContainer.push('0'); }
                                    if (key === 'Crafter') { allQuotationsCrafterContainer.push('0'); }
                                }

                                stringLinkToRoute += '<div id="imageEmailSent_' +
                                    data.truckid[key][key2]['lastQuotationId'] +
                                    '_' + data.truckid[key][key2]['routeId'] + '" >';

                                if (data.truckid[key][key2]['emailsentremovefromroute'] == 1) {
                                    stringLinkToRoute += '<img id="emailRemove_' +
                                        data.truckid[key][key2]['routeId'] +
                                        '" class="imageEmailSent" src="/cms/images/icons/email-sent-red.png" ' +
                                        'title="remove from route email sent" alt="remove from route email sent" />';
                                }

                                if (data.truckid[key][key2]['emailsentdelivery'] == 1) {
                                    stringLinkToRoute += '<img id="emailDelivery_' +
                                        data.truckid[key][key2]['routeId'] +
                                        '" class="imageEmailSent" src="/cms/images/icons/email-sent-blue.png" ' +
                                        'title="delivery email sent" alt="delivery email sent" />';
                                }

                                if (data.truckid[key][key2]['emailsentrackpickup'] == 1) {
                                    stringLinkToRoute += '<img id="emailPickup_' +
                                        data.truckid[key][key2]['routeId'] +
                                        '" class="imageEmailSent" src="/cms/images/icons/email-sent-green.png" ' +
                                        'title="pickup email sent" alt="pickup email sent" />';
                                }

                                stringLinkToRoute += '</div>';

                                stringLinkToRoute += '<a style="float:right;margin-right:5px;" ' +
                                    'href="#" style="text-decoration: underline;" ' +
                                    'onclick="window.open(\'/cms/crm/crm.php?tabroute=route&quotationid=' +
                                    data.truckid[key][key2]['lastQuotationId']+'\',\'_blank\');">' +
                                    '<img src="/cms/images/icons/delivery-truck.png" alt="route" ></img></a>';

                            }

                        } // if (data.truckid[key][key2]['mapExtraPoi'] == 1) {

                        //-- sendMailToClient
                        var stringLinkCallClient = '';
                        if (data.truckid[key][key2]['sendMailToClient'] === '1') {
                            stringLinkCallClient = '<div style="float:right;padding:0px;margin-right:0px;"'+
                            'onmouseover="ddrivetip(\''+data.truckid[key][key2]['callOrEmailNotes']+'\',\'white\', 300)"'+
                            'onmouseout="hideddrivetip()" class="showYesCont" ><a style="float:right;padding-right:10px;" '+
                            'href="#" style="text-decoration: underline;" '+
                            'onclick="window.open(\'/cms/crm/crm.php?tabadmin=general&quotationId='
                            + data.truckid[key][key2]['callOrEmailNotesQuotation']+'\',\'_blank\');">'+
                            '<img src="/cms/images/icons/auricular-phone-symbol-in-a-circle.png" alt="route" style="width:24px;" ></img></a></div>';
                        }

                        //-- quoteIds
                        var stringLinkInfo = '';
                        if (data.truckid[key][key2]['quoteIds'] !== '') {
                            stringLinkInfo = '<div style="float:right;padding:0px;margin-right:0px;" onmouseover="ddrivetip(\''+data.truckid[key][key2]['quoteIds']+'\',\'white\', 300)" onmouseout="hideddrivetip()" class="showYesCont" ><a style="float:right;padding-right:10px;" href="#" style="text-decoration: underline;" onclick="window.open(\'/cms/crm/crm.php?tabadmin=delivery&quotationId=' + data.truckid[key][key2]['lastQuotationId']+'\',\'_blank\');"><img src="/cms/images/icons/info.png" alt="route" style="width:24px;" ></img></a></div>';
                        }

                        //-- notes
                        var stringLinkInfo2 = '';
                        if (data.truckid[key][key2]['notes'] !== '') {
                            stringLinkInfo = '<div style="float:right;padding:0px;margin-right:-4px;" onmouseover="ddrivetip(\''+data.truckid[key][key2]['notes']+'\',\'white\', 300)" onmouseout="hideddrivetip()" class="showYesCont" ><a style="float:right;padding-right:10px;" href="#" style="text-decoration: underline;" onclick="window.open(\'/cms/crm/crm.php?tabadmin=general&quotationId=' + data.truckid[key][key2]['lastQuotationId']+'\',\'_blank\');"><img src="/cms/images/icons/info.png" alt="route" style="width:24px;" ></img></a></div>';
                        }

                        var countAmountCharacheters = data.truckid[key][key2]['subject'].length;
                        var subjectShort = data.truckid[key][key2]['subject'];
                        var sMeters = data.truckid[key][key2]['sMeters'];
                        sMetersTotaal += parseFloat(sMeters);

                        var sMetersUnder40 = data.truckid[key][key2]['sMetersUnder40'];
                        sMetersUnder40Totaal += parseFloat(sMetersUnder40);

                        sMetersString = sMetersString + ' - ' + data.truckid[key][key2]['domestic'] + ' - ' + sMeters + ' - ';

                        var maxCharacters = 20;
                        if (countAmountCharacheters > maxCharacters) {
                            subjectShort = subjectShort.substring(0, maxCharacters);
                            subjectShort = subjectShort + '...';
                        }

                        var domestic = data.truckid[key][key2]['domestic'];
                        if (echoAllRed === true) {
                            subjectShort = '<span style="color:red;">' + subjectShort + '</span>';
                            domestic = '<span style="color:red;">' + domestic + '</span>';
                        }

                        var stringObjectArrayType = { key:data.truckid[key][key2]['lastQuotationId'], value:stringIdName };
                        objQuotationType.push(stringObjectArrayType);

                        $("#sortable"+data.truckid[key][key2]['truckId']).append('<li class="ui-state-default ui-sortable-handle" id="'+stringIdName+data.truckid[key][key2]['routeId']+'"> '
                            +'<span id="sortableSpanAlphabet">'+alphabet[keyPlusOne]+'</span>'+''+geenLng+ ' '
                            +'<span id="sortableSpanStatusImage">' + stringStatusImage + '</span> ' + ' '
                            +stringLinkToRoute
                            +stringLinkCallClient
                            +stringLinkInfo
                            +stringLinkInfo2
                            +'<span id="sortableSpanDomestic"><b>'+domestic+'</b></span>'+''+geenLng+ ' '
                            +'<span id="sortableSpanSubject">' + subjectShort + '</span> ' + ' '
                            +'<span id="sortableSpanSubjectUnder"></span> ' + ' '
                            +'<div style="clear: both;"></div>'
                            +'</li>');

                        +'<span id="sortableSpanDomestic"><b>'+domestic+' - '+sMeters+'</b></span>'+''+geenLng+ ' '

                    }

                    $("#totaalAantalMeters"+data.truckid[key][0]['truckId']).append('meters totaal: ' + sMetersTotaal.toFixed(2) + ' - ' + sMetersUnder40Totaal.toFixed(2));

                    var stringAllQuotations = '';

                    //-- afhalen
                    if (data.truckid[key][0]['truckId'] === '3') {
                        stringAllQuotations = allQuotationsAfhalen;
                    }

                    //-- volvo
                    if (data.truckid[key][0]['truckId'] === '2') {

                        stringPerQuotation = '';
                        stringPerQuotation+= '<table border="1" class="table-hover">';
                        stringPerQuotation+= '' +
                            '<tr>' +
                            '<td class="tablePopupList"><b>Bedrijfsnaam</b></td>' +
                            '<td class="tablePopupList"><b>Plaats</b></td>' +
                            '<td class="tablePopupList"><b><a id="selectAllDeliveryLinkVolvo" href="#">Leveren SelectAll</a></b></td>' +
                            '<td class="tablePopupList"><b><a id="selectAllPickupLinkVolvo" href="#">Retour SelectAll</a></b></td>' +
                            '<td class="tablePopupList"><b><a id="selectAllRemoveLinkVolvo" href="#">Route Verwijderen SelectAll</a></b></td>' +
                            '</tr>';

                        // '<td class="tablePopupList"><b>Type</b></td>' +
                        // '<td class="tablePopupList"><b>routeId</b></td>' +

                        for (var i=0; i < allQuotationsVolvo.length; i++) {

                            stringPerQuotation+= '<tr>';
                            stringPerQuotation+= '<td class="tablePopupList"> ' + allQuotationsVolvoSubject[i] + '</td>';
                            stringPerQuotation+= '<td class="tablePopupList"><b>' + allQuotationsVolvoDomestic[i] + '</b></td>';

                            //-- aidpoi_ = Point Of Intrest bijv: St.Joris of Wienerberger.
                            if (objQuotationType[i].value !== 'aidc_' && objQuotationType[i].value !== 'aidpoi_') {

                                if (allQuotationsVolvoEmailSentDelivery[i] === '1') {
                                    stringPerQuotation+= '<td class="tablePopupList tablePopupListCenter">'+
                                        '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsVolvo[i] + '_dlv_' + allQuotationsVolvoRouteId[i] + '_Volvo">'+
                                        '<img class="popupEmailSent" src="/cms/images/icons/email-sent-blue.png" title="delivery email sent" alt="delivery email sent">'+
                                        '</div>'+
                                        '</td>';
                                } else {
                                    stringPerQuotation += '<td class="tablePopupList tablePopupListCenter">'+
                                        '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsVolvo[i] + '_dlv_' + allQuotationsVolvoRouteId[i] + '_Volvo">'+
                                        '<input type="checkbox" class="selectAllDeliveryLinkVolvo"  name="chkQuotations" value="' + allQuotationsVolvo[i] + '_dlv_' + allQuotationsVolvoRouteId[i] + '_Volvo">'+
                                        '</div>'+
                                        '</td>';
                                }

                            } else {
                                stringPerQuotation += '<td class="tablePopupList tablePopupListCenter">&nbsp;</td>';
                            }

                            if (objQuotationType[i].value === 'aidc_') {
                                if (allQuotationsVolvoEmailRackPickup[i] === '1') {
                                    stringPerQuotation+= '<td class="tablePopupList tablePopupListCenter">'+
                                        '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsVolvo[i] + '_pick_' + allQuotationsVolvoRouteId[i] + '_Volvo">'+
                                        '<img class="popupEmailSent" src="/cms/images/icons/email-sent-green.png" title="pickup email sent" alt="pickup email sent">'+
                                        '</div>'+
                                        '</td>';
                                } else {
                                    stringPerQuotation+= '<td class="tablePopupList tablePopupListCenter">'+
                                        '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsVolvo[i] + '_pick_' + allQuotationsVolvoRouteId[i] + '_Volvo">'+
                                        '<input type="checkbox" class="selectAllPickupLinkVolvo" name="chkQuotations" value="'+allQuotationsVolvo[i]+'_pick_' + allQuotationsVolvoRouteId[i] + '_Volvo">' +
                                        '</div>'+
                                        '</td>';
                                }
                            } else {
                                stringPerQuotation+= '<td class="tablePopupList tablePopupListCenter">&nbsp;</td>';
                            }

                            if (allQuotationsVolvoEmailRemoveFromRoute[i] === '1') {
                                stringPerQuotation+= '<td class="tablePopupList tablePopupListCenter">'+
                                    '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsVolvo[i] + '_rempick_' + allQuotationsVolvoRouteId[i] + '_Volvo">'+
                                    '<img class="popupEmailSent" src="/cms/images/icons/email-sent-red.png" title="remove from route email sent" alt="remove from route email sent">' +
                                    '</div>'+
                                    '</td>';
                            } else {

                                //-- kijk of er een lever of afhaal mail verzonden is.
                                if (objQuotationType[i].value !== 'aidc_' && allQuotationsVolvoEmailSentDelivery[i] === '1')
                                {

                                    stringPerQuotation += '<td class="tablePopupList tablePopupListCenter">' +
                                        '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsVolvo[i] + '_remdlv_' + allQuotationsVolvoRouteId[i] + '_Volvo">' +
                                        '<input type="checkbox" class="selectAllRemoveLinkVolvo" name="chkQuotations" value="' + allQuotationsVolvo[i] + '_remdlv_' + allQuotationsVolvoRouteId[i] + '_Volvo">' +
                                        '</div>' +
                                        '</td>';
                                }
                                else if (objQuotationType[i].value === 'aidc_' && allQuotationsVolvoEmailRackPickup[i] === '1')
                                {

                                    stringPerQuotation += '<td class="tablePopupList tablePopupListCenter">' +
                                        '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsVolvo[i] + '_rempick_' + allQuotationsVolvoRouteId[i] + '_Volvo">' +
                                        '<input type="checkbox" class="selectAllRemoveLinkVolvo" name="chkQuotations" value="' + allQuotationsVolvo[i] + '_rempick_' + allQuotationsVolvoRouteId[i] + '_Volvo">' +
                                        '</div>' +
                                        '</td>';

                                } else {

                                    if (objQuotationType[i].value === 'aidc_') {

                                        //-- lege waarde
                                        stringPerQuotation += '<td class="tablePopupList tablePopupListCenter">' +
                                            '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsVolvo[i] + '_rempick_' + allQuotationsVolvoRouteId[i] + '_Volvo">'+
                                            '</div>'+
                                            '</td>';

                                    } else {

                                        //-- lege waarde
                                        stringPerQuotation += '<td class="tablePopupList tablePopupListCenter">' +
                                            '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsVolvo[i] + '_remdlv_' + allQuotationsVolvoRouteId[i] + '_Volvo">'+
                                            '</div>'+
                                            '</td>';

                                    }

                                }


                            }
                            stringPerQuotation += '</div>';

                            stringPerQuotation+= '</tr>';

                        }

                        stringPerQuotation+= '</table>';
                        stringAllQuotations = stringPerQuotation;

                    } // if (data.truckid[key][0]['truckId'] === '2') {

                    //-- iveco start -----------------------------------------------------------------------------------

                    if (data.truckid[key][0]['truckId'] === '1') {

                        stringPerQuotation = '';
                        stringPerQuotation+= '<table border="1" class="table-hover">';
                        stringPerQuotation+= '' +
                            '<tr>' +
                            '<td class="tablePopupList" width="100"><b>Bedrijfsnaam</b></td>' +
                            '<td class="tablePopupList" width="100"><b>Plaats</b></td>' +
                            '<td class="tablePopupList"><b><a id="selectAllDeliveryLinkIveco" href="#">Leveren SelectAll</a></b></td>' +
                            '<td class="tablePopupList"><b><a id="selectAllPickupLinkIveco" href="#">Retour SelectAll</a></b></td>' +
                            '<td class="tablePopupList"><b><a id="selectAllRemoveLinkIveco" href="#">Route Verwijderen SelectAll</a></b></td>' +
                            '</tr>';

                        for (var i=0; i < allQuotationsIveco.length; i++) {

                            stringPerQuotation+= '<tr>';
                            stringPerQuotation+= '<td class="tablePopupList"> ' + allQuotationsIvecoSubject[i] + '</td>';
                            stringPerQuotation+= '<td class="tablePopupList"><b>' + allQuotationsIvecoDomestic[i] + '</b></td>';

                            //-- aidpoi_ = Point Of Intrest bijv: St.Joris of Wienerberger.
                            if (objQuotationType[i].value !== 'aidc_' && objQuotationType[i].value !== 'aidpoi_') {

                                if (allQuotationsIvecoEmailSentDelivery[i] === '1') {
                                    stringPerQuotation+= '<td class="tablePopupList tablePopupListCenter">'+
                                        '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsIveco[i] + '_dlv_' + allQuotationsIvecoRouteId[i] + '_Iveco">'+
                                        '<img class="popupEmailSent" src="/cms/images/icons/email-sent-blue.png" title="delivery email sent" alt="delivery email sent">'+
                                        '</div>'+
                                        '</td>';
                                } else {
                                    stringPerQuotation += '<td class="tablePopupList tablePopupListCenter">'+
                                        '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsIveco[i] + '_dlv_' + allQuotationsIvecoRouteId[i] + '_Iveco">'+
                                        '<input type="checkbox" class="selectAllDeliveryLinkIveco"  name="chkQuotations" value="' + allQuotationsIveco[i] + '_dlv_' + allQuotationsIvecoRouteId[i] + '_Iveco">'+
                                        '</div>'+
                                        '</td>';
                                }

                            } else {
                                stringPerQuotation += '<td class="tablePopupList tablePopupListCenter">&nbsp;</td>';
                            }

                            if (objQuotationType[i].value === 'aidc_') {
                                if (allQuotationsIvecoEmailRackPickup[i] === '1') {
                                    stringPerQuotation+= '<td class="tablePopupList tablePopupListCenter">'+
                                        '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsIveco[i] + '_pick_' + allQuotationsIvecoRouteId[i] + '_Iveco">'+
                                        '<img class="popupEmailSent" src="/cms/images/icons/email-sent-green.png" title="pickup email sent" alt="pickup email sent">'+
                                        '</div>'+
                                        '</td>';
                                } else {
                                    stringPerQuotation+= '<td class="tablePopupList tablePopupListCenter">'+
                                        '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsIveco[i] + '_pick_' + allQuotationsIvecoRouteId[i] + '_Iveco">'+
                                        '<input type="checkbox" class="selectAllPickupLinkIveco" name="chkQuotations" value="'+allQuotationsIveco[i]+'_pick_' + allQuotationsIvecoRouteId[i] + '_Iveco">' +
                                        '</div>'+
                                        '</td>';
                                }
                            } else {
                                stringPerQuotation+= '<td class="tablePopupList tablePopupListCenter">&nbsp;</td>';
                            }

                            if (allQuotationsIvecoEmailRemoveFromRoute[i] === '1') {
                                stringPerQuotation+= '<td class="tablePopupList tablePopupListCenter">'+
                                    '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsIveco[i] + '_rempick_' + allQuotationsIvecoRouteId[i] + '_Iveco">'+
                                    '<img class="popupEmailSent" src="/cms/images/icons/email-sent-red.png" title="remove from route email sent" alt="remove from route email sent">' +
                                    '</div>'+
                                    '</td>';
                            } else {

                                //-- kijk of er een lever of afhaal mail verzonden is.

                                if (objQuotationType[i].value !== 'aidc_' && allQuotationsIvecoEmailSentDelivery[i] === '1')
                                {

                                    stringPerQuotation += '<td class="tablePopupList tablePopupListCenter">' +
                                        '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsIveco[i] + '_remdlv_' + allQuotationsIvecoRouteId[i] + '_Iveco">'+
                                        '<input type="checkbox" class="selectAllRemoveLinkIveco" name="chkQuotations" value="' + allQuotationsIveco[i] + '_remdlv_' + allQuotationsIvecoRouteId[i] + '_Iveco">' +
                                        '</div>'+
                                        '</td>';

                                } else if (objQuotationType[i].value === 'aidc_' && allQuotationsIvecoEmailRackPickup[i] === '1') {

                                    stringPerQuotation += '<td class="tablePopupList tablePopupListCenter">' +
                                        '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsIveco[i] + '_rempick_' + allQuotationsIvecoRouteId[i] + '_Iveco">'+
                                        '<input type="checkbox" class="selectAllRemoveLinkIveco" name="chkQuotations" value="' + allQuotationsIveco[i] + '_rempick_' + allQuotationsIvecoRouteId[i] + '_Iveco">' +
                                        '</div>'+
                                        '</td>';

                                } else {

                                    if (objQuotationType[i].value === 'aidc_' && allQuotationsIvecoEmailRackPickup[i] === '1') {

                                        //-- lege waarde
                                        stringPerQuotation += '<td class="tablePopupList tablePopupListCenter">' +
                                            '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsIveco[i] + '_rempick_' + allQuotationsIvecoRouteId[i] + '_Iveco">'+
                                            '</div>'+
                                            '</td>';

                                    } else {

                                        //-- lege waarde
                                        stringPerQuotation += '<td class="tablePopupList tablePopupListCenter">' +
                                            '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsIveco[i] + '_remdlv_' + allQuotationsIvecoRouteId[i] + '_Iveco">'+
                                            '</div>'+
                                            '</td>';

                                    }

                                }

                            }
                            stringPerQuotation += '</div>';

                            stringPerQuotation+= '</tr>';

                        }

                        stringPerQuotation+= '</table>';
                        stringAllQuotations = stringPerQuotation;

                    }

                    //-- iveco einde -----------------------------------------------------------------------------------

                    //-- Crafter start -----------------------------------------------------------------------------------

                    if (data.truckid[key][0]['truckId'] === '4') {

                        stringPerQuotation = '';
                        stringPerQuotation+= '<table border="1" class="table-hover">';
                        stringPerQuotation+= '' +
                            '<tr>' +
                            '<td class="tablePopupList" width="100"><b>Bedrijfsnaam</b></td>' +
                            '<td class="tablePopupList" width="100"><b>Plaats</b></td>' +
                            '<td class="tablePopupList"><b><a id="selectAllDeliveryLinkCrafter" href="#">Leveren SelectAll</a></b></td>' +
                            '<td class="tablePopupList"><b><a id="selectAllPickupLinkCrafter" href="#">Retour SelectAll</a></b></td>' +
                            '<td class="tablePopupList"><b><a id="selectAllRemoveLinkCrafter" href="#">Route Verwijderen SelectAll</a></b></td>' +
                            '</tr>';

                        for (var i=0; i < allQuotationsCrafter.length; i++) {

                            stringPerQuotation+= '<tr>';
                            stringPerQuotation+= '<td class="tablePopupList"> ' + allQuotationsCrafterSubject[i] + '</td>';
                            stringPerQuotation+= '<td class="tablePopupList"><b>' + allQuotationsCrafterDomestic[i] + '</b></td>';

                            //-- aidpoi_ = Point Of Intrest bijv: St.Joris of Wienerberger.
                            if (objQuotationType[i].value !== 'aidc_' && objQuotationType[i].value !== 'aidpoi_') {

                                if (allQuotationsCrafterEmailSentDelivery[i] === '1') {
                                    stringPerQuotation+= '<td class="tablePopupList tablePopupListCenter">'+
                                        '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsCrafter[i] + '_dlv_' + allQuotationsCrafterRouteId[i] + '_Crafter">'+
                                        '<img class="popupEmailSent" src="/cms/images/icons/email-sent-blue.png" title="delivery email sent" alt="delivery email sent">'+
                                        '</div>'+
                                        '</td>';
                                } else {
                                    stringPerQuotation += '<td class="tablePopupList tablePopupListCenter">'+
                                        '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsCrafter[i] + '_dlv_' + allQuotationsCrafterRouteId[i] + '_Crafter">'+
                                        '<input type="checkbox" class="selectAllDeliveryLinkCrafter"  name="chkQuotations" value="' + allQuotationsCrafter[i] + '_dlv_' + allQuotationsCrafterRouteId[i] + '_Crafter">'+
                                        '</div>'+
                                        '</td>';
                                }

                            } else {
                                stringPerQuotation += '<td class="tablePopupList tablePopupListCenter">&nbsp;</td>';
                            }

                            if (objQuotationType[i].value === 'aidc_') {
                                if (allQuotationsCrafterEmailRackPickup[i] === '1') {
                                    stringPerQuotation+= '<td class="tablePopupList tablePopupListCenter">'+
                                        '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsCrafter[i] + '_pick_' + allQuotationsCrafterRouteId[i] + '_Crafter">'+
                                        '<img class="popupEmailSent" src="/cms/images/icons/email-sent-green.png" title="pickup email sent" alt="pickup email sent">'+
                                        '</div>'+
                                        '</td>';
                                } else {
                                    stringPerQuotation+= '<td class="tablePopupList tablePopupListCenter">'+
                                        '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsCrafter[i] + '_pick_' + allQuotationsCrafterRouteId[i] + '_Crafter">'+
                                        '<input type="checkbox" class="selectAllPickupLinkCrafter" name="chkQuotations" value="'+allQuotationsCrafter[i]+'_pick_' + allQuotationsCrafterRouteId[i] + '_Crafter">' +
                                        '</div>'+
                                        '</td>';
                                }
                            } else {
                                stringPerQuotation+= '<td class="tablePopupList tablePopupListCenter">&nbsp;</td>';
                            }

                            if (allQuotationsCrafterEmailRemoveFromRoute[i] === '1') {
                                stringPerQuotation+= '<td class="tablePopupList tablePopupListCenter">'+
                                    '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsCrafter[i] + '_rempick_' + allQuotationsCrafterRouteId[i] + '_Crafter">'+
                                    '<img class="popupEmailSent" src="/cms/images/icons/email-sent-red.png" title="remove from route email sent" alt="remove from route email sent">' +
                                    '</div>'+
                                    '</td>';
                            } else {

                                //-- kijk of er een lever of afhaal mail verzonden is.

                                if (objQuotationType[i].value !== 'aidc_' && allQuotationsCrafterEmailSentDelivery[i] === '1')
                                {

                                    stringPerQuotation += '<td class="tablePopupList tablePopupListCenter">' +
                                        '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsCrafter[i] + '_remdlv_' + allQuotationsCrafterRouteId[i] + '_Crafter">'+
                                        '<input type="checkbox" class="selectAllRemoveLinkCrafter" name="chkQuotations" value="' + allQuotationsCrafter[i] + '_remdlv_' + allQuotationsCrafterRouteId[i] + '_Crafter">' +
                                        '</div>'+
                                        '</td>';

                                } else if (objQuotationType[i].value === 'aidc_' && allQuotationsCrafterEmailRackPickup[i] === '1') {

                                    stringPerQuotation += '<td class="tablePopupList tablePopupListCenter">' +
                                        '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsCrafter[i] + '_rempick_' + allQuotationsCrafterRouteId[i] + '_Crafter">'+
                                        '<input type="checkbox" class="selectAllRemoveLinkCrafter" name="chkQuotations" value="' + allQuotationsCrafter[i] + '_rempick_' + allQuotationsCrafterRouteId[i] + '_Crafter">' +
                                        '</div>'+
                                        '</td>';

                                } else {

                                    if (objQuotationType[i].value === 'aidc_' && allQuotationsCrafterEmailRackPickup[i] === '1') {

                                        //-- lege waarde
                                        stringPerQuotation += '<td class="tablePopupList tablePopupListCenter">' +
                                            '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsCrafter[i] + '_rempick_' + allQuotationsCrafterRouteId[i] + '_Crafter">'+
                                            '</div>'+
                                            '</td>';

                                    } else {

                                        //-- lege waarde
                                        stringPerQuotation += '<td class="tablePopupList tablePopupListCenter">' +
                                            '<div id="chkQuotationsUpdateRouteDisplay' + allQuotationsCrafter[i] + '_remdlv_' + allQuotationsCrafterRouteId[i] + '_Crafter">'+
                                            '</div>'+
                                            '</td>';

                                    }

                                }

                            }
                            stringPerQuotation += '</div>';

                            stringPerQuotation+= '</tr>';

                        }

                        stringPerQuotation+= '</table>';
                        stringAllQuotations = stringPerQuotation;

                    }

                    //-- Crafter einde -----------------------------------------------------------------------------------


                    if (data.truckid[key][0]['truckId'] === '1') {
                        var truckname = 'Iveco';
                    }

                    if (data.truckid[key][0]['truckId'] === '2') {
                        var truckname = 'Volvo';
                    }

                    if (data.truckid[key][0]['truckId'] === '3') {
                        var truckname = 'Afhalen';
                    }

                    if (data.truckid[key][0]['truckId'] === '4') {
                        var truckname = 'Crafter';
                    }

                    var stringModalQuotations = ' '+

                        '<div class="modal-dialog" role="document" id="sendMultipleEmailsContainer'+data.truckid[key][0]['truckId']+'">' +
                        '<div class="modal-content" id="sendMultipleEmailsContainerQuotations'+data.truckid[key][0]['truckId']+'">' +
                        '<form name="formSendMultipleEmailsContainerQuotations'+data.truckid[key][0]['truckId']+'" class="formSendMultipleEmailsContainerQuotations">' +
                        '<h1>'+truckname+'</h1>' +
                        '<table>'+
                        '<tr>'+
                        '<td>'+
                        stringAllQuotations +
                        '<button type="button" class="btn btn-primary btnSendEmailToQuotationList">Send Email</button>' +
                        '<div id="showErrorsSendEmail"></div>' +
                        '<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">x</span></button>'+
                        '</td>'+
                        '</tr>'+
                        '</form>' +
                        '</div>' +
                        '</div>';

                    $("#modalLoginForm"+data.truckid[key][0]['truckId']).html(stringModalQuotations);

                }

                <?php

                    foreach ($oRoutes AS $keyRoute => $valueRoute) {

                        if (!isset($countTruckIds[$valueRoute->truckId]['count'])) {
                            $countTruckIds[$valueRoute->truckId]['count'] = 0;
                        } else {
                            $countTruckIds[$valueRoute->truckId]['count'] = $countTruckIds[$valueRoute->truckId]['count'] + 1;
                        }

                        //-- weer eindigen in bladel
                        echo '$("#sortable'.$valueRoute->truckId.'").append(\'<li class="ui-state-default ui-state-disabled" id="aid_00000">Raambrug 9, Bladel</li>\');' . "\n";
                        echo '$("#sortable'.$valueRoute->truckId.'").sortable({ items: "li:not(.ui-state-disabled)" });';

                        ?>

                        $("#sortable<?php echo $valueRoute->truckId; ?>").sortable({

                            update: function(event, ui) {

                                var dataSortable = $(this).sortable("toArray");
                                //-- adding an empty turns it into a string
                                dataSortable = dataSortable + '';
                                var dataWithCommaReplaced = dataSortable.replace(/,/g, "-");
                                updateRank(dataWithCommaReplaced);

                            },
                            over: function () {
                                removeIntent = false;
                            },
                            out: function () {
                                removeIntent = true;
                            },
                            beforeStop: function (event, ui) {

                                if (removeIntent === true) {

                                    console.log('updateRouteDisplay.php 984');

                                    ui.item.hide();
                                    substring = "aidc_";
                                    var routeIdListItem = ui.item.attr("id");

                                    substringpoi = "aidpoi_";

                                    //-- check inbouwen die ziet of een verwijder mail verzonden is.
                                    //-- mail verzonden image moet bestaan.
                                    //-- kan ik hier de quotation of routeId tonen?

                                    if (routeIdListItem.includes(substringpoi) === true) {

                                        var routeId = routeIdListItem.replace('aidpoi_', '');
                                        if (confirm('Weet je zeker dat je deze "Point of intrest" (POI) wilt verwijderen?')) {
                                            ui.item.remove();
                                            dissconnectPoi(routeId);
                                        } else {
                                            ui.item.show();
                                        }

                                    } else {

                                        if (routeIdListItem.includes(substring) === false) {

                                            console.log('updateRouteDisplay.php 1014');

                                            var routeId = routeIdListItem.replace('aid_', '');
                                            var nameDLV = '#emailDelivery_' +  routeId;
                                            var namePICK = '#emailPickup_' +  routeId;
                                            var nameREM = '#emailRemove_' +  routeId;
                                            var nonRemovalErrorFlag = false;
                                            var nonRemovalError = '';

                                            if ($(nameDLV).length > 0 && $(nameREM).length == 0) {

                                                var nonRemovalError = "Deze route mag je nog niet verwijderen," +
                                                    " omdat deze aflever route " +
                                                    "ingeplanned staat en er is al een mail naar de klant gestuurd." +
                                                    " Stuur eerst een verwijder mail om deze route te kunnen " +
                                                    "verwijderen.";
                                                nonRemovalErrorFlag = true;

                                            }

                                            if ($(namePICK).length > 0 && $(nameREM).length == 0) {

                                                var nonRemovalError = "Deze route mag je nog niet verwijderen," +
                                                " omdat deze ophaal route " +
                                                "ingeplanned staat en er is al een mail naar de klant gestuurd." +
                                                " Stuur eerst een verwijder mail om deze route te kunnen " +
                                                "verwijderen.";
                                                nonRemovalErrorFlag = true;

                                            }

                                            if (nonRemovalError !== '') {
                                                alert('nonRemovalError: ' + nonRemovalError);
                                            }

                                            //-- if removeEmail_23127 does not exist then you may not remove item
                                            //-- Eerst een email naar klant sturen voordat je kan verwijderen.

                                            if (nonRemovalErrorFlag === false) {
                                                if (confirm('Weet je zeker dat je deze route wilt verwijderen?')) {
                                                    ui.item.remove();
                                                    dissconnectRoute(routeId);
                                                } else {
                                                    ui.item.show();
                                                }
                                            } else {
                                                ui.item.show();
                                            }

                                        } else {

                                            console.log('updateRouteDisplay.php 1072');

                                            // substring = "aidc_";
                                            // container

                                            var routeIdContainer = routeIdListItem.replace('aidc_', '');
                                            var nameDLV = '#emailDelivery_' +  routeIdContainer;
                                            var namePICK = '#emailPickup_' +  routeIdContainer;
                                            var nameREM = '#emailRemove_' +  routeIdContainer;
                                            nonRemovalError = '';
                                            nonRemovalErrorFlag = false;

                                            if ($(nameDLV).length > 0 && $(nameREM).length == 0) {

                                                var nonRemovalError = "Deze container ophaal route mag je nog niet verwijderen," +
                                                    " omdat deze aflever route " +
                                                    "ingeplanned staat en er is al een mail naar de klant gestuurd." +
                                                    " Stuur eerst een verwijder mail om deze route te kunnen " +
                                                    "verwijderen.";
                                                nonRemovalErrorFlag = true;

                                            }

                                            if ($(namePICK).length > 0 && $(nameREM).length == 0) {

                                                var nonRemovalError = "Deze container ophaal route mag je nog niet verwijderen," +
                                                    " omdat deze ophaal route " +
                                                    "ingeplanned staat en er is al een mail naar de klant gestuurd." +
                                                    " Stuur eerst een verwijder mail om deze route te kunnen " +
                                                    "verwijderen.";
                                                nonRemovalErrorFlag = true;

                                            }

                                            if (nonRemovalError !== '') {
                                                alert('nonRemovalError: ' + nonRemovalError);
                                            }

                                            if (nonRemovalErrorFlag === false) {

                                                alert('bakken en rekken mogen niet zomaar ontkoppeld worden. Deze moeten dan handmatig worden ontkoppeld. er zit namelijk een beveiliging in function removeRouteId($quotationId)(. alleen iets verwijderen als de status lager dan 60 is.');

                                                ui.item.show();

                                                /*
                                                if (confirm('Weet je zeker dat je deze route wilt verwijderen?')) {
                                                    ui.item.remove();
                                                    dissconnectRoute(routeId);
                                                } else {
                                                    ui.item.show();
                                                }
                                                */

                                            } else {
                                                ui.item.show();
                                            }

                                        }
                                    } // if (routeIdListItem.includes(substringpoi) === true) {
                                }
                            } // beforeStop: function (event, ui) {
                                                
                        });

                        <?php

                    } // foreach ($oRoutes AS $keyRoute => $valueRoute) {


                ?>

                var truckIdDoesExist = [];

                for (var key in arrayOfTruckIdAndName) {

                    //-- maak een array van truckId's die niet bestaan.

                    if (data.truckid[arrayOfTruckIdAndName[key]['truckname']]) {
                        truckIdDoesExist.push(data.truckid[arrayOfTruckIdAndName[key]['truckid']]);
                    }

                    if (!data.truckid[arrayOfTruckIdAndName[key]['truckname']]) {

                        $("#sortable"+arrayOfTruckIdAndName[key]['truckid']).html('<li class="ui-state-default ui-state-disabled">Er zijn geen adressen ingeplanned.</li>');
                        $(".sortabletitle"+arrayOfTruckIdAndName[key]['truckid']).css("display", "none");
                        $("#sortable"+arrayOfTruckIdAndName[key]['truckid']).css("display", "none");
                        $("#waypointSubmitTruckId"+arrayOfTruckIdAndName[key]['truckid']).css("display", "none");
                        $("#changeDateSubmitTruckId"+arrayOfTruckIdAndName[key]['truckid']).css("display", "none");
                        $("#fldChangeDateRoute"+arrayOfTruckIdAndName[key]['truckid']+"container").css("display", "none");
                        $("#modalLoginForm"+arrayOfTruckIdAndName[key]['truckid']).css("display", "none");
                        $("#verstuurAangepasteRouteButtonFormGeneral"+arrayOfTruckIdAndName[key]['truckid']).css("display", "none");

                    } else {

                        //-- popup niet tonen van de
                        if (arrayOfTruckIdAndName[key]['truckid'] !== '3') {
                            $("#verstuurAangepasteRouteButtonFormGeneral"+arrayOfTruckIdAndName[key]['truckid']).css("display", "inline-block");
                        }

                        $(".sortabletitle"+arrayOfTruckIdAndName[key]['truckid']).css("display", "inline-block");
                        $("#sortable"+arrayOfTruckIdAndName[key]['truckid']).css("display", "inline-block");
                        $("#waypointSubmitTruckId"+arrayOfTruckIdAndName[key]['truckid']).css("display", "inline-block");
                        $("#changeDateSubmitTruckId"+arrayOfTruckIdAndName[key]['truckid']).css("display", "inline-block");
                        $("#fldChangeDateRoute"+arrayOfTruckIdAndName[key]['truckid']+"container").css("display", "inline-block");

                    }
                }

                if (callback && typeof(callback) === "function") {
                    callback();
                }

            },
            error: function(jqXHR, exception) {
                console.log('error code d3d3dedh84774473erutr53534');
                if (jqXHR.status === 0) {
                    console.log('Not connect.n Verify Network.');
                    alert('Not connect.n Verify Network.');
                } else if (jqXHR.status == 404) {
                    console.log('Requested page not found. [404]');
                    alert('Requested page not found. [404]');
                } else if (jqXHR.status == 500) {
                    console.log('Internal Server Error [500].');
                    alert('Internal Server Error [500].');
                } else if (exception === 'parsererror') {
                    console.log('Requested JSON parse failed. possible that its not well formatted. code 45g45h6k8iy');
                    alert('Requested JSON parse failed. possible that its not well formatted. code 45g45h6k77865648iy');
                    console.log('error dateValue: ' + dateValue);
                } else if (exception === 'timeout') {
                    console.log('Time out error.');
                    alert('Time out error.');
                } else if (exception === 'abort') {
                    console.log('Ajax request aborted.');
                    alert('Ajax request aborted.');
                } else {
                    console.log('Uncaught Error.n' + jqXHR.responseText);
                    alert('Uncaught Error.n' + jqXHR.responseText);
                }
            }
        }); // $.ajax({

    } // function updateRouteOverview(dateValue) {
    
  
</script>
