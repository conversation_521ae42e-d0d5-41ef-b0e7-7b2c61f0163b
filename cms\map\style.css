html {
  height: 100%
}

body {
  height: 100%;
  margin: 0px;
  padding: 0px;
  font: normal 11px <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
}

#mapcontainer {
  display: flex;
  justify-content: space-between;
  height: 100%;
}

#left {
  width: 250px;
  max-width: 100%;
  height: 100%;
}

#map_canvas {
  height: 100%;
  flex-grow: 1;
}

/* #map_canvas { width: 500px; height: 500px;  float: left; } */

#right_canvas {
  width: 450px;
  max-width: 100%;
  height: 100%;
  padding-left: 20px;
  overflow: scroll;
  background-color: #EEEEEE;
}

#locationOrders {
  overflow: scroll;
  width: 100%;
  height: 496px;
  padding: 0 10px;
}
#locationOrders .order {
  padding: 5px 0;
}

ul li.note {
  color: #FF0000;
}

ul li a {
  color: #404040;
}

#locationOrders p.title {
  margin: 0;
}

#locationOrders .container {
  background: #efefef;
  padding: 0;
  margin-bottom: 5px;
}

#locationOrders .container img {
  vertical-align: bottom;
}

#locationOrders a.title {
  color: #404040;
  text-decoration: none;
  font-weight: bold;
  display: block;
  background-color: #cccccc;
  padding: 10px;
}

#locationOrders .containerdetails {
  padding: 5px 10px;
}

#locationOrders ul {
  list-style-type: none;
  margin: 5px 0;
  padding: 0;
}

#locationOrders ul li {
  padding: 3px 0;
}

ul.minderenter {
  margin: 0px 0px 0px 0px;
}

.seperationGreyLine {
  border-top: 1px solid grey;
  margin-top: 10px;
}

#containerList {
  /* border:1px solid red; */
  border-top: 1px solid grey;
  border-bottom: 1px solid grey;
  padding: 10px;
}
#containerList ul {
  padding-left: 25px;
}

ul.checkboxStatusExtras {
  -webkit-padding-start: 0px;
}

.checkboxStatusExtras > li {
  list-style-type: none;
  display: inline-block;
}

.checkboxStatusExtras > li > label {
  width: 140px;
}


ul.checkboxStatus {
  padding: 0;
}

.checkboxStatus > li {
  list-style-type: none;
  display: inline-block;
}

.checkboxStatus > li > label {
  width: 50px;
}

#mapsearch {
  margin: 10px 0px 0px 0px;
  height: 22px;
  padding: 2px
}


body {
  font-family: Arial, Helvetica, sans-serif;
}

table {
  font-size: 1em;
}

.ui-draggable, .ui-droppable {
  background-position: top;
}

#sortable {
  border: 1px solid red;
  width: 100px;
}

#sortable {
  list-style-type: none;
  margin: 0;
  padding: 0;
  width: 60%;
}

#sortable li {
  margin: 0 3px 3px 3px;
  padding: 0.4em;
  padding-left: 1.5em;
  font-size: 10px;
  height: 18px;
}

#sortable li span {
  position: absolute;
  margin-left: -1.3em;
}

.ui-state-default {
  border: 1px solid black;
}

.Highlighted a {
  background-color: Green !important;
  background-image: none !important;
  color: White !important;
  font-weight: bold !important;
  font-size: 12pt;
}

.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight {
  border-collapse: collapse;
}

ul.ui-sortable {
  border: 0px solid #89cff0;
  padding: 0px;
  margin: 0px;
}

li.ui-state-default {
  list-style-type: none;
  font-size: 14px;
  padding: 10px 10px 0px 10px;
  min-height: 27px;
  width: 390px; /** MOET BLIJVEN STAAN ANDERS WERKT SLEPEN NIET */
  max-width: 100%;
  border: 1px solid #A9A9A9;
  color: #000000;
  background: #FFFFFF;
}

div.ui-datepicker {
  /* met deze font size kun je de grote van de calender bepalen */
  font-size: 14px;
}
#fldDateRoute4weeksContainer {
  display: inline-block;
}

#rightOfRouteCalenderContainer {
  border: 0 solid deepskyblue;
  width: 100px;
  float: right;
  margin: 15px 30px 5px 5px;
}

.rdebtn {
  color: #000;
  font-size: 14px;
  background: #ebebeb;
  border: 1px solid #bbb !important;
  border-radius: 4px;
  padding: 6px 12px;
  font-weight: 500;
  transition: all .2s ease;
  outline: none !important;
}
.rdebtn:hover {
  color: #ce000c;
  border-color: #ce000c;
  box-shadow: 2px 2px 4px #dddbdb;
  text-decoration: none;
}

.directions-panel {
  margin-left: 15px;
  padding: 5px 10px;
  background-color: white;
  display: none;
}

li.ui-state-disabled {
  display: none;
}

#datepicker .hasDatepicker {
  width: 200px;
  border: 1px solid red;
}

ul.ui-sortable > li {
  border-top: none;
}

ul.ui-sortable > li:nth-child(1) {
  border-top: 1px solid #c5c5c5 !important;
}

ul.ui-sortable > li:nth-child(2) {
  border-top: 1px solid #c5c5c5 !important;
}

/* grijs licht blauw */

.ui-sortable-helper {
  background: #e6f2ff;
  border: 1px solid #c5c5c5;
  width: 400px !important;
}

ul.ui-sortable > li {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.10);
  transition: all 0.3s cubic-bezier(.10, .8, .25, 1);
}

div > .ui-datepicker {
  border: 0px solid red !important;
  display: inline-block !important;
}

div > .hasDatepicker {
  display: inline-block !important;
  margin-top: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.10);
  transition: all 0.3s cubic-bezier(.10, .8, .25, 1);
}

#sortableSpanAlphabet {
  float: left;
  padding-right: 5px;
  width: 12px;
}

#sortableSpanStatusImage {
  float: left;
  padding-right: 5px;
  width: 17px;
}

#sortableSpanDomestic {
  float: left;
  padding-right: 5px;
  padding-bottom: 10px;
}

#sortableSpanSubject {
  float: left;
  padding-right: 5px;
  padding-bottom: 10px;
  overflow: hidden;
  width: 140px;
  height: 16px;
}

#sortableSpanSubjectUnder {
  float: left;
  padding-right: 5px;
  padding-bottom: 10px;
  overflow: hidden;
  width: 140px;
  height: 2px;
}

#sortableSpanExtraInfo {
  color: red;
  float: left;
  padding-bottom: 10px;
}

.showYesCont {
  margin: 0px;
  float: left;
  position: relative;
  cursor: pointer;
  width: 30px;
  text-align: center;
  padding: 0px;
  margin-top: 0px;
}

.showYesCont:hover span {
  display: block;
}

#dhtmltooltip {
  position: absolute;
  width: 150px;
  border: 1px solid black;
  padding: 10px;
  background-color: #EEEEEE !important;
  color: black;
  visibility: hidden;
  z-index: 100;
  /*Remove below line to remove shadow. Below line should always appear last within this CSS*/
  filter: progid:DXImageTransform.Microsoft.Shadow(color=gray, direction=135);
  font-size: 16px;
}

.printStatusLink {
  float: left;
  padding-top: 12px;
  padding-left: 10px;
}

.printStatusLink > a:link, a:visited, a:active {
  color: #000000;
  text-decoration: underline;
}

.printStatusLink > a:hover {
  color: #FF0000;
}

.sortabletitle {
  float: left;
  font-size: 14px;
  display: none;
  font-weight: bold;
  margin: 9px 0px 0px 0px;
}

.totaalAantalMeters {
  width: 150px;
  text-align: left;
  float: left;
  padding-top: 13px;
  padding-left: 0px;
  margin-left: 10px;
}

.changeDateRoute {
  margin-top: 0px !important;
  margin-left: 10px;
  width: 80px;
  height: 20px !important;
  padding-left: 5px;
  padding-top: 1px;
  box-shadow: none !important;
  border: 1px solid #A9A9A9 !important;
}

.changeDateRoute4Weeks {
  margin-top: 0px !important;
  margin-left: 10px;
  width: 80px;
  height: 20px !important;
  padding-left: 5px;
  padding-top: 1px;
  box-shadow: none !important;
  border: 1px solid #A9A9A9 !important;
}

.typeAmountInner {
  width: 27px;
  float: left;
  border: 0px solid green;
}

.containerTypeAmount {
  border: 0px solid red;
  float: right;
  width: 27px;
  margin-top: 4px;
  margin-left: 10px;
}

.imageEmailSent {
  float: right;
  width: 8px;
  height: 16px;
  margin: 4px 0px 0px 0px;
}

.mapButtonsTopLeft {
  align-items: flex-start;
  text-align: center;
  cursor: default;
  color: buttontext;
  background-color: buttonface;
  box-sizing: border-box;
  padding: 2px 6px 3px;
  border-width: 2px;
  border-style: outset;
  border-color: buttonface;
  border-image: initial;
  font: 400 13.3333px Arial;
}

#mapButtonsTopLeftContainer {
  margin: 11px 0px;
}

.formSendMultipleEmailsContainerQuotations {
  padding: 20px;
}

.colorCheckboxesMap {
  padding: 7px;
}
.colorCheckboxesMap ul {
  margin-bottom: 10px;
}

.tablePopupList {
  padding: 5px;
}

#btnSendEmailToQuotationList {
  margin-top: 20px;
  float: left;
}

.clsBtnSendEmailToQuotationList {
  margin-top: 20px;
  float: left;
}

button.close {
  padding: 27px 31px 0px 0px;
}

.tablePopupListCenter {
  text-align: center;
}

.popupEmailSent {
  width: 14px;
  height: 14px;
}

#showErrorsSendEmail {
  min-width: 275px;
  min-height: 20px;
  float: left;
  margin: 29px 0px 0px 15px;
}

.btnUpdateContainerClass {
  width: 99px;
  height: 16px;
  font-size: 11px;
  padding: 0px;
}

#waypointSubmitWeekTitle {
  font-size: 14px;
  font-weight: bold;
}

@media only screen and (max-width: 860px){
  #mapcontainer {
    display: block;
  }
  #mapcontainer #left {
    width: 100%;
    height: auto;
  }
  #locationOrders {
    height: 150px;
  }
}
