function ajaxUpdateDateRoute(transportId, newChangeDate, newChangeDateOld, callback) {

    $.ajax({
        type: 'GET',
        dataType: "json",
        data: {transportId: transportId, date: newChangeDate, olddate: newChangeDateOld},
        url: "ajax/ajaxUpdateDateRoute.php",
        success: function(data) {
            console.log('update gelukt g6MVe0Ed');
            if (data.error !== '') {
                alert('data.error: ' + data.error);
                console.log('data.error: ' + data.error);
                console.log('transportId: ' + transportId);
                console.log('newChangeDateOldSplit: ' + newChangeDateOldSplit);
            } else {

                if (callback && typeof(callback) === "function") {
                    callback();
                }

            }


        },
        error: function(jqXHR, exception) {
            console.log('error code LXzkp9J9');
            if (jqXHR.status === 0) {
                console.log('Not connect.n Verify Network.');
                alert('Not connect.n Verify Network.');
            } else if (jqXHR.status == 404) {
                console.log('Requested page not found. [404]');
                alert('Requested page not found. [404]');
            } else if (jqXHR.status == 500) {
                console.log('Internal Server Error [500].');
                alert('Internal Server Error [500].');
            } else if (exception === 'parsererror') {
                console.log('Requested JSON parse failed. possible that its not well formatted. code 5hed35rer');
                alert('Requested JSON parse failed. possible that its not well formatted. code 5hed35rer');
            } else if (exception === 'timeout') {
                console.log('Time out error.');
                alert('Time out error.');
            } else if (exception === 'abort') {
                console.log('Ajax request aborted.');
                alert('Ajax request aborted.');
            } else {
                console.log('Uncaught Error.n' + jqXHR.responseText);
                alert('Uncaught Error.n' + jqXHR.responseText);
            }
        }
    }); // $.ajax({


}

