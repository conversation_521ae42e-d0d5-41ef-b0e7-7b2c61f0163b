<?php


  class clsCRMRouteGetAddressForAjax {

    /**
     * @param $quotationId
     * @return bool|stdClass
     */
    public function getRouteAddressDataForAjax($quotationId) {

      $sqlAddressDeliveryId = "SELECT
                                        addressDeliveryId
                                    FROM
                                        rde_b1mo.quotations_extra
                                    WHERE
                                        quotationId = '" . $quotationId . "'
                                    LIMIT 1
                                    ";

      $oAddressDeliveryId = clsDBSingleton::processSingleObjectQuery($sqlAddressDeliveryId);

      if($oAddressDeliveryId !== false) {

        $addressDeliveryId = $oAddressDeliveryId->addressDeliveryId;

        if($addressDeliveryId > 0 && $addressDeliveryId != '' && is_numeric($addressDeliveryId)) {

          $sqlAddresses = "SELECT
                                        street, nr, extension, zipcode, domestic, country, longitude, latitude
                                    FROM
                                        rde_cms.crm_addresses
                                    WHERE
                                        addressId = '" . $addressDeliveryId . "'
                                    LIMIT 1
                                    ";

          $oAddressData = clsDBSingleton::processSingleObjectQuery($sqlAddresses);

          $oReturnAddressData = new stdClass;
          $oReturnAddressData->street = $oAddressData->street;
          $oReturnAddressData->nr = $oAddressData->nr;
          $oReturnAddressData->extension = $oAddressData->extension;
          $oReturnAddressData->zipcode = $oAddressData->zipcode;
          $oReturnAddressData->domestic = $oAddressData->domestic;
          $oReturnAddressData->country = $oAddressData->country;
          $oReturnAddressData->longitude = $oAddressData->longitude;
          $oReturnAddressData->latitude = $oAddressData->latitude;
          $oReturnAddressData->addressDeliveryId = $addressDeliveryId;

          return $oReturnAddressData;

        }
        else {

          return false;

        }


      }
      else {

        return false;

      }

    }

  }